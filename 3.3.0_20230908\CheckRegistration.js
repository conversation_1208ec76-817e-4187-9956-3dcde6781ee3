import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import APIS from "../apis.js";

import baseurl from "./Data/baseurl.js"

let baseurls = baseurl[0]
const URL = baseurls.url  + "/services/external";
const SLEEP_DURATION = 2;
const USERNAME = "<EMAIL>";
const PASSWORD = "supersupeR4!";
const USERNAME_2 = "<EMAIL>";


// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 200,
            iterations: 2,
            maxDuration: '10m',
        },
    },
};

// Test scenario
export default function () {

    const paramscheckregistration = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': 'ASDFGH@WOMF'
            //  ,'Cookie' : 'idjs=FDD47A1F028E0E6900095FC1D1395777'
        }
    };

    // Random e-mail suffix

    let apis = APIS[0][exec.vu.idInTest - 1];

    if (apis == null) {
        apis = {
            "username": ""
            , "nik": ""
            , "nohp": ""
        };
    }
    // Get User Profile (user/s/profiles)
    const generateCheckRegistationRequest = JSON.stringify({

        audit: { callerId: "UserIBAA" }
        , dataType: "NIK"
        , userData: "3511000101808100"

    });
    const url = BASE_URL + "/user/checkRegistration";
    const checkRegistration = http.post(url, generateCheckRegistationRequest, paramscheckregistration);
    // console.log(generateLinkInvitation.body());
    sleep(SLEEP_DURATION);

    check(checkRegistration, {
        'is status 200': (r) => r.status === 200

    });

    console.log(checkRegistration.body);

}
