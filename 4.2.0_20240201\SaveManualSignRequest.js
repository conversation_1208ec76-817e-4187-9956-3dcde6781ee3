import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./Data/authorization-library.js";
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const url = baseurls.url  + "/services/document/s/insertManualSign";
const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 1,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {

    let auths = auth[0][3];

    const paramsUploadManualSignRequest = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'Authorization': auths['bearerToken']
        }
    };

    const generateUploadManualSignRequest = JSON.stringify({

  "documentTemplateCode": "DOC-CST2"+ [exec.vu.idInTest],
  "documentTemplateName": "Custom Doc1",
  "documentTemplateDescription": "Custom Doc1",
  "isActive": "1",
"isSequence": "1",
  "numberOfPage": 1,
  "paymentSignTypeCode": "TTD",
  
  "useSignQr" : "0",
  "tenantCode": "ADINS",
"vendorCode": "VIDA",
  "isSignLocOnly": "0",
  
  "signer": [
    
   
    {
      "signerTypeCode": "CUST",
      "signTypeCode": "TTD",
      "signPage": 1,
	"seqNo": "1",
      "signLocation": {
        llx: 70.66665649414062,
        lly: 166.85334350585936,
        urx: 200.66665649414062,
        ury: 231.85334350585936,
      },
      "transform": "translate3d(24px, 24px, 0px)",
      "position": "{\"x\": 8.78,\"y\":8.78,\"w\":45.86,\"h\":22.93}",
      "positionVida":  "{\"x\":25,\"y\":752,\"w\":130,\"h\":65}",
      "positionPrivy":  "{\"x\":33,\"y\":33,\"w\":198,\"h\":106}"
    }
  ],
 
       
 "audit": {
            "callerId": "<EMAIL>"
        },
"documentExample": ""

    });
    console.log(generateUploadManualSignRequest);
	//console.log(url);
    const checkUploadManualSignRequest = http.post(url, generateUploadManualSignRequest, paramsUploadManualSignRequest);

    sleep(SLEEP_DURATION);

    check(checkUploadManualSignRequest, {
        'is status 200': (r) => r.status === 200
    });

    console.log( checkUploadManualSignRequest.json);

}
