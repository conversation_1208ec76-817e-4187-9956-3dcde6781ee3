{"cells": [{"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Authorization 1:\n", "  Username: <EMAIL>\n", "  Password: Password123!\n", "  Bearer Token: Bearer ASHj6MWkdelNTqWkKqHCloP14YM=\n", "  Message: zRGQaG59B8dHnobnG0PoHsJHcHXJwITv/Qjw4D5ypM6pWtHWzl2/feN8WO9NlTIfTcm0GKYeE3/CkzuYnn8SMT/dk9QBdDQJAoslPcoqYOKcwJVymVnSbNJvgtJhBGPfUsfeeZU5AXkIj5WQL5cEMg==\n", "\n", "Authorization 2:\n", "  Username: HELPDESK@ADINS\n", "  Password: password\n", "  Bearer Token: Bearer FOgQl/RSoRLZleAJ1G23qnOa3mM=\n", "  Message: zRGQaG59B8dHnobnG0PoHsJHcHXJwITv/Qjw4D5ypM6pWtHWzl2/feN8WO9NlTIfRMxV/b+92GawFIZKSsD0bgeTiXHvEqwzqiQ/Qkg8VrvUG1SC0bz+E90gcIONTQoJ00xd06t60RI05xc3y2BzwA==\n", "\n", "Authorization 3:\n", "  Username: <EMAIL>\n", "  Password: password\n", "  Bearer Token: Bearer meYc/5cp9lgmh7Oc4fLlIrVHK3E=\n", "  Message: zRGQaG59B8dHnobnG0PoHsJHcHXJwITv/Qjw4D5ypM6pWtHWzl2/feN8WO9NlTIfvcDeqB/I/vfn5kle/rPZ+N3Uy9CvO7BiUWYQ4CbCtaSye7fTAFn7NTaPXPFqMAN5fCZjEbh1Fjlo0YqkEoKPpg==\n", "\n", "Authorization 4:\n", "  Username: <EMAIL>\n", "  Password: Password123!\n", "  Bearer Token: Bearer Ep/8TRjXBjYOMI9BcFNd1O/VprA=\n", "  Message: zRGQaG59B8dHnobnG0PoHsJHcHXJwITv/Qjw4D5ypM6pWtHWzl2/feN8WO9NlTIf4zYE2otxTcogJWJ/1gIQNZg3/NIRucilX5ZItPvt3a3lhe8hCKW9RKlcLvPs2BxoltOb9AiLZJPCx8FJpHeh8A==\n", "\n", "Authorization 5:\n", "  Username: <EMAIL>\n", "  Password: Password123!\n", "  Bearer Token: <PERSON><PERSON> bUdUye+/SJGmz0LI2f6JlckdJgU=\n", "  Message: zRGQaG59B8dHnobnG0PoHsJHcHXJwITv/Qjw4D5ypM6pWtHWzl2/feN8WO9NlTIf4hbc8KUITUQMEGVxV5+mmluKGl7dLRWp+sJPub3+Cpum6M49F466GZQFJAbE6LrY7yBDd1Na+sk6VRDVrdaq/A==\n", "\n", "Authorization 6:\n", "  Username: <EMAIL>\n", "  Password: Password123!\n", "  Bearer Token: Bearer SHxWEof8PfjP/chT1+L6lmZ69UU=\n", "  Message: zRGQaG59B8dHnobnG0PoHsJHcHXJwITv/Qjw4D5ypM6pWtHWzl2/feN8WO9NlTIf9GEGw7eeg6eMgTRwD7/CpSmX7CMiWWHwUaL1XEQXM7/KhkXE7SZh1aKm7riQAsRRgUpksepwVH1fgUeT7frxa0IeQldfw+nQ7i3J4v4VjV4=\n", "\n"]}], "source": ["import execjs\n", "import requests\n", "\n", "def getBearer(baseurl, username, password):\n", "    url = baseurl +'/oauth/token'\n", "    headers = {\n", "        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:131.0) Gecko/20100101 Firefox/131.0',\n", "        'Accept': 'application/json, text/plain, */*',\n", "        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'\n", "    }\n", "    data = {\n", "        'client_id': 'frontend',\n", "        'grant_type': 'password',\n", "        'username': username,\n", "        'password': password\n", "    }\n", "\n", "    response = requests.post(url, headers=headers, data=data)\n", "    if response.status_code == 200:\n", "        return response.json().get('access_token')\n", "    else:\n", "        print(f\"Failed to get token for {username}. Status code: {response.status_code}\")\n", "        print(url)\n", "        return None\n", "    \n", "def getMsg(username):\n", "    url = 'https://mockdata.gyandi.dev/encrypt'\n", "    headers = {\n", "        'Accept': 'application/json, */*',\n", "        'Content-Type': 'application/json'\n", "    }\n", "    data = {\n", "        \"tenantCode\": \"ADINS\",\n", "        \"officeCode\": \"HO\",\n", "        \"email\": username,\n", "        \"aesKey\" : \"CniQHdpCOsruzNcv\",\n", "        \"menu\" : \"inquiry\",\n", "        \"env\" : \"DEV\"\n", "    }\n", "\n", "    response = requests.post(url, headers=headers, json=data)\n", "    if response.status_code == 200:\n", "        return response.json().get('encrypt_msg')\n", "    else:\n", "        print(f\"Failed to get msg for {username}. Status code: {response.status_code}\")\n", "        return None\n", "\n", "# Read the JavaScript code from the file\n", "with open('Data/authorization-library.js', 'r') as file:\n", "    js_code = file.read()\n", "\n", "with open('Data/baseurl.js', 'r') as fileurl:\n", "    js_code_url = fileurl.read()    \n", "\n", "# Compile and execute JavaScript\n", "context = execjs.compile(js_code)\n", "authorizations = context.eval('authorization')\n", "\n", "context = execjs.compile(js_code_url)\n", "urls = context.eval('baseurl')\n", "\n", "baseurl = urls[0]['url']\n", "\n", "# Loop through each authorization and update bearerToken\n", "for authorization in authorizations:\n", "    username = authorization['username']\n", "    password = authorization['password']\n", "    newBearerToken = getBearer(baseurl, username, password)\n", "    msg = getMsg(username)\n", "\n", "    if newBearerToken:\n", "        authorization['bearerToken'] = 'Bearer ' + newBearerToken\n", "    \n", "    if msg:\n", "        authorization['msg'] = msg\n", "\n", "with open('Data/authorization-library.js', 'w') as file:\n", "    file.write(\"const authorization = \\n\")\n", "    file.write(\"    [\\n\")\n", "    for authorization in authorizations:\n", "        file.write(\"        {\\n\")\n", "        file.write(f\"            username: '{authorization['username']}',\\n\")\n", "        file.write(f\"            password: '{authorization['password']}',\\n\")\n", "        file.write(f\"            bearerToken: '{authorization['bearerToken']}',\\n\")\n", "        file.write(f\"            msg: '{authorization['msg']}'\\n\")\n", "        file.write(\"        },\\n\")\n", "    file.write(\"    ];\\n\")\n", "    file.write(\"module.exports = authorization;\\n\")\n", "\n", "for index, authorization in enumerate(authorizations):\n", "    username = authorization['username']\n", "    password = authorization['password']\n", "    bearer_token = authorization['bearerToken']\n", "    msg = authorization['msg']\n", "\n", "    print(f\"Authorization {index + 1}:\")\n", "    print(f\"  Username: {username}\")\n", "    print(f\"  Password: {password}\")\n", "    print(f\"  Bearer <PERSON>ken: {bearer_token}\")\n", "    print(f\"  Message: {msg}\")\n", "    print()\n"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["unsignDocument 1:\n", "  refNumber: LOADTEST4.9.0-797\n", "  documentId: 00163E03-FD4A-93EB-11EF-5ABD1695F5A0\n", "  encryptedDocId: LGBQrM0qzf0BkbhTVLenuo9wlSZhtYTeaek78Cds0oVBsnkqu33aArr/doWe6twf\n", "\n", "unsignDocument 2:\n", "  refNumber: LOADTEST4.9.0-795\n", "  documentId: 00163E01-2F6A-BA2A-11EF-5ABD19933010\n", "  encryptedDocId: z0i6jbLS81bMqeinzStJVP99ZaQ6KSBf3Yiu30q2LsxK9tY%2B5cOUhmJ76Kkjt3vq\n", "\n", "unsignDocument 3:\n", "  refNumber: LOADTEST4.9.0-790\n", "  documentId: 00163E01-2F6A-BA2A-11EF-5ABD19B53700\n", "  encryptedDocId: z0i6jbLS81bMqeinzStJVLcSw8XDz4CrZaHjrI%2B7D0RYwZWAZSSkBtqybTjyRGAw\n", "\n", "unsignDocument 4:\n", "  refNumber: LOADTEST4.9.0-794\n", "  documentId: 00163E01-2F6A-BA2A-11EF-5ABD19CABAD0\n", "  encryptedDocId: z0i6jbLS81bMqeinzStJVJAg5x5g1taDBIT/e1vacQ/nhUKe24Q05JBJb3ym/AvE\n", "\n", "unsignDocument 5:\n", "  refNumber: LOADTEST4.9.0-792\n", "  documentId: 00163E01-2F6A-BA2A-11EF-5ABD19CDEF20\n", "  encryptedDocId: z0i6jbLS81bMqeinzStJVC7LIKYwtpo5YdZ67PejRioPhaSe3JGF/WXQwRFDE1pu\n", "\n", "unsignDocument 6:\n", "  refNumber: LOADTEST4.9.0-793\n", "  documentId: 00163E01-2F6A-BA2A-11EF-5ABD19D56930\n", "  encryptedDocId: z0i6jbLS81bMqeinzStJVPDEwFtDkIAmlt2v22rsD3y0OtB5ReulxN5zv5hLpgaT\n", "\n", "unsignDocument 7:\n", "  refNumber: LOADTEST4.9.0-793\n", "  documentId: 00163E01-2F6A-BA2A-11EF-5ABD19D56930\n", "  encryptedDocId: z0i6jbLS81bMqeinzStJVPDEwFtDkIAmlt2v22rsD3y0OtB5ReulxN5zv5hLpgaT\n", "\n", "unsignDocument 8:\n", "  refNumber: LOADTEST4.9.0-796\n", "  documentId: 00163E01-2F6A-BA2A-11EF-5ABD1A6992E0\n", "  encryptedDocId: z0i6jbLS81bMqeinzStJVK6Jx3Cg/kXQA3E7aQB5R6t21EJrHFqDYXEafPFVUVpH\n", "\n", "unsignDocument 9:\n", "  refNumber: LOADTEST4.9.0-799\n", "  documentId: 00163E01-2F6A-BA2A-11EF-5ABD1B54B630\n", "  encryptedDocId: z0i6jbLS81bMqeinzStJVGDH3qAV9ttEA0s91P9%2BUn3C4tS4uEGWFWfVPKUzpDl7\n", "\n", "unsignDocument 10:\n", "  refNumber: LOADTEST4.9.0-791\n", "  documentId: 00163E01-2F6A-BA2A-11EF-5ABD1B39DB30\n", "  encryptedDocId: z0i6jbLS81bMqeinzStJVFxrVYA4jvv%2BFFraA7x8PUDPtiqAvgx11Da2/Evje3SN\n", "\n", "unsignDocument 11:\n", "  refNumber: LOADTEST4.9.0-798\n", "  documentId: 00163E01-2F6A-BA2A-11EF-5ABD1C9F5EA1\n", "  encryptedDocId: HTGlUm%2BfTOQSHtftInWK5DHOzlgld3LZqKiQRlliJd0hNNyqPt96eXAMXTO5pPsl\n", "\n", "unsignDocument 1:\n", "  refNumber: SDT-24-17-09-1\n", "  documentId: 00155D0B-7502-AA4C-11EF-74DDE9150CC1\n", "  encryptedDocId: \n", "\n", "unsignDocument 2:\n", "  refNumber: TEST-WA\n", "  documentId: 2C6DC18B-56E1-BF11-11EF-71B470D390A0\n", "  encryptedDocId: \n", "\n", "unsignDocument 3:\n", "  refNumber: TEST-STAMP-PRIVY-0913\n", "  documentId: 00155D0B-7502-BEC9-11EF-71A70EA00561\n", "  encryptedDocId: \n", "\n", "unsignDocument 4:\n", "  refNumber: VIDA-Jmprx9cizs1726211569\n", "  documentId: 00155D0B-7502-AF4B-11EF-719FA1D82310\n", "  encryptedDocId: \n", "\n", "unsignDocument 5:\n", "  refNumber: VIDA-Jmprx9cizs1726211569\n", "  documentId: 00155D0B-7502-AF4B-11EF-719F96DED760\n", "  encryptedDocId: \n", "\n", "unsignDocument 6:\n", "  refNumber: TEST-STAMP-VIDA-004\n", "  documentId: 00155D0B-7502-AF02-11EF-717CFD6B0F31\n", "  encryptedDocId: \n", "\n", "unsignDocument 7:\n", "  refNumber: SDT-VIDA-24-09-12-1\n", "  documentId: 00155D0B-7502-AF02-11EF-70E6AAD54FB0\n", "  encryptedDocId: \n", "\n", "unsignDocument 8:\n", "  refNumber: SDT-24-9-9-1-A\n", "  documentId: 00155D0B-7502-A2C3-11EF-6E53C2DE3C60\n", "  encryptedDocId: \n", "\n", "unsignDocument 9:\n", "  refNumber: TEST-STAMP-VIDA-01\n", "  documentId: 00155D0B-7502-97D1-11EF-6C637691E651\n", "  encryptedDocId: \n", "\n", "unsignDocument 10:\n", "  refNumber: TEST-STAMP-VIDA-02\n", "  documentId: 2C6DC18B-56E1-9875-11EF-6C636FFDF091\n", "  encryptedDocId: \n", "\n", "unsignDocument 11:\n", "  refNumber: TEST-STAMP-VIDA\n", "  documentId: 00155D0B-7502-97D1-11EF-6C6342FF3B81\n", "  encryptedDocId: \n", "\n", "unsignDocument 12:\n", "  refNumber: Malvintest_sprint_document_priority_baru\n", "  documentId: 00163E03-FD4A-8041-11EF-32AAB16E26F0\n", "  encryptedDocId: \n", "\n", "unsignDocument 13:\n", "  refNumber: Malvintest_sprint_document_priority_baru\n", "  documentId: 00163E03-FD4A-8041-11EF-32AAB1F38390\n", "  encryptedDocId: \n", "\n", "unsignDocument 14:\n", "  refNumber: TEST-SIGN-VIDA-01\n", "  documentId: 00163E03-FD4A-AA0E-11EF-2FA787F096B1\n", "  encryptedDocId: \n", "\n", "unsignDocument 15:\n", "  refNumber: CERT-EXPIRED-VIDA-02\n", "  documentId: 00163E03-FD4A-AFF7-11EF-26E00E7122C0\n", "  encryptedDocId: \n", "\n", "unsignDocument 16:\n", "  refNumber: MANUAL-PRIVY-REV39-02\n", "  documentId: 00163E03-FD4A-819D-11EF-2634E6E1FE60\n", "  encryptedDocId: \n", "\n", "unsignDocument 17:\n", "  refNumber: TEST-EXPIRED-CERT-VIDA\n", "  documentId: 00163E03-FD4A-B32D-11EF-25438F03C441\n", "  encryptedDocId: \n", "\n", "unsignDocument 18:\n", "  refNumber: TEST-EXPIRED-CERT-01\n", "  documentId: 00163E03-FD4A-B32D-11EF-25432B17A0A0\n", "  encryptedDocId: \n", "\n", "unsignDocument 19:\n", "  refNumber: TEST_REVIEW_16\n", "  documentId: 00163E01-4AA7-B3A2-11EE-FC957433E9F1\n", "  encryptedDocId: \n", "\n", "unsignDocument 20:\n", "  refNumber: TEST_REVIEW_120\n", "  documentId: 00163E01-4AA7-B3A2-11EE-FC95743B3CF1\n", "  encryptedDocId: \n", "\n"]}], "source": ["import execjs\n", "import requests\n", "    \n", "def encrypt(docId):\n", "    url = 'https://mockdata.gyandi.dev/encrypt_documentId'\n", "\n", "    headers = {\n", "        'Accept': 'application/json, */*',\n", "        'Content-Type': 'application/json'\n", "    }\n", "\n", "    data = {\n", "        \"documentId\": docId,\n", "        \"aesKey\": \"CniQHdpCOsruzNcv\"\n", "    }\n", "\n", "    response = requests.post(url, headers=headers, json=data)\n", "    if response.status_code == 200:\n", "        return response.json().get('encrypt_msg')\n", "    else:\n", "        print(f\"Failed to get msg for {docId}. Status code: {response.status_code}\")\n", "        return None\n", "\n", "# Load JS file and retrieve document list\n", "with open('Data/unsignDocumentId.js', 'r') as file:\n", "    js_code = file.read()\n", "\n", "with open('Data/DocumentId.js', 'r') as file:\n", "    js_code_signed = file.read()\n", "\n", "context = execjs.compile(js_code)\n", "unsignDocuments = context.eval('encryptedDocId')\n", "\n", "context = execjs.compile(js_code_signed)\n", "signedDocuments = context.eval('signDoc')\n", "\n", "for unsignDocument in unsignDocuments:\n", "    documentId = unsignDocument['docId']\n", "    msg = encrypt(documentId)\n", "    \n", "    if msg:\n", "        unsignDocument['encryptedDocId'] = msg\n", "\n", "for signedDocument in signedDocuments:\n", "    documentId = signedDocument['docId']\n", "    msg = encrypt(documentId)\n", "    \n", "    if msg:\n", "        signedDocument['encryptedDocId'] = msg\n", "\n", "with open('Data/unsignDocumentId.js', 'w') as file:\n", "    file.write(\"const encryptedDocId = \\n\")\n", "    file.write(\"    [\\n\")\n", "    for unsignDocument in unsignDocuments:\n", "        file.write(\"        {\\n\")\n", "        file.write(f\"            refNumber: '{unsignDocument['refNumber']}',\\n\")\n", "        file.write(f\"            docId: '{unsignDocument['docId']}',\\n\")\n", "        file.write(f\"            encryptedDocId: '{unsignDocument['encryptedDocId']}'\\n\")\n", "        file.write(\"        },\\n\")\n", "    file.write(\"    ];\\n\")\n", "    file.write(\"module.exports = encryptedDocId;\\n\")\n", "\n", "with open('Data/DocumentId.js', 'w') as file:\n", "    file.write(\"const signDoc = \\n\")\n", "    file.write(\"    [\\n\")\n", "    for signedDocument in signedDocuments:\n", "        file.write(\"        {\\n\")\n", "        file.write(f\"            refNumber: '{signedDocument['refNumber']}',\\n\")\n", "        file.write(f\"            docId: '{signedDocument['docId']}',\\n\")\n", "        file.write(f\"            encryptedDocId: '{signedDocument['encryptedDocId']}'\\n\")\n", "        file.write(\"        },\\n\")\n", "    file.write(\"    ];\\n\")\n", "    file.write(\"module.exports = signDoc;\\n\")\n", "\n", "for index, unsignDocument in enumerate(unsignDocuments):\n", "    refNumber = unsignDocument['refNumber']\n", "    documentId = unsignDocument['docId']\n", "    msg = unsignDocument['encryptedDocId']\n", "\n", "    print(f\"unsignDocument {index + 1}:\")\n", "    print(f\"  refNumber: {refNumber}\")\n", "    print(f\"  documentId: {documentId}\")\n", "    print(f\"  encryptedDocId: {msg}\")\n", "    print()\n", "\n", "for index, signedDocument in enumerate(signedDocuments):\n", "    refNumber = signedDocument['refNumber']\n", "    documentId = signedDocument['docId']\n", "    msg = signedDocument['encryptedDocId']\n", "\n", "    print(f\"unsignDocument {index + 1}:\")\n", "    print(f\"  refNumber: {refNumber}\")\n", "    print(f\"  documentId: {documentId}\")\n", "    print(f\"  encryptedDocId: {msg}\")\n", "    print()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}