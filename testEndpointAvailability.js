import http from "k6/http";
import { check, sleep } from "k6";
import baseurl from "./master script/Data/baseurl.js";
import authorization from "./master script/Data/authorization-library.js";

// Configuration
let baseurls = baseurl[0];
const authData = authorization[3]; // <EMAIL>

// Test different endpoints and methods
const endpointsToTest = [
    {
        url: baseurls.url + "/services/user-management/s/insertUserManagement",
        method: "POST",
        description: "Original insertUserManagement endpoint"
    },
    {
        url: baseurls.url + "/services/user-management/insertUserManagement",
        method: "POST", 
        description: "insertUserManagement without /s/"
    },
    {
        url: baseurls.url + "/services/user/s/insertUserManagement",
        method: "POST",
        description: "insertUserManagement under /user/"
    },
    {
        url: baseurls.url + "/services/user-management/s/insertUserManagement",
        method: "GET",
        description: "GET method to check if endpoint exists"
    }
];

export default function () {
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': authData.bearerToken
        }
    };

    // Minimal request body for testing
    const requestBody = JSON.stringify({
        "audit": {
            "callerId": "<EMAIL>"
        },
        "fullName": "Test User",
        "tenantCode": "ADINS",
        "loginId": "<EMAIL>",
        "roleCode": "ADMCLIENT",
        "password": "ADINS",
        "officeCode": "0071"
    });

    console.log("Testing endpoint availability and methods...");

    endpointsToTest.forEach((endpoint, index) => {
        console.log(`\n--- Test ${index + 1}: ${endpoint.description} ---`);
        console.log(`URL: ${endpoint.url}`);
        console.log(`Method: ${endpoint.method}`);

        let response;
        if (endpoint.method === "GET") {
            response = http.get(endpoint.url, requestHeader);
        } else {
            response = http.post(endpoint.url, requestBody, requestHeader);
        }
        
        console.log(`Status: ${response.status}`);
        console.log(`Response: ${response.body.substring(0, 200)}${response.body.length > 200 ? '...' : ''}`);

        // Analyze response
        if (response.status === 404) {
            console.log(`✗ Endpoint not found`);
        } else if (response.status === 405) {
            console.log(`✗ Method not allowed`);
        } else if (response.status === 401) {
            console.log(`✗ Authentication failed`);
        } else if (response.status === 200) {
            try {
                const responseBody = JSON.parse(response.body);
                if (responseBody.status) {
                    if (responseBody.status.code === 0) {
                        console.log(`✓ SUCCESS: Endpoint works!`);
                    } else {
                        console.log(`⚠ Endpoint exists but has business logic error: ${responseBody.status.message}`);
                    }
                }
            } catch (e) {
                console.log(`✓ Endpoint exists (status 200) but response format unknown`);
            }
        } else {
            console.log(`? Unexpected status: ${response.status}`);
        }

        sleep(1);
    });

    // Test if we can get any information about available roles
    console.log(`\n--- Additional Test: Check available roles ---`);
    const roleCheckUrl = baseurls.url + "/services/user-management/roles";
    const roleResponse = http.get(roleCheckUrl, requestHeader);
    console.log(`Role check URL: ${roleCheckUrl}`);
    console.log(`Status: ${roleResponse.status}`);
    console.log(`Response: ${roleResponse.body.substring(0, 200)}${roleResponse.body.length > 200 ? '...' : ''}`);
}

export let options = {
    vus: 1,
    iterations: 1,
};
