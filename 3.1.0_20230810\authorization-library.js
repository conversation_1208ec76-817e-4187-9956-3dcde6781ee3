/*
    Setiap mau run yang membutuhkan token, run ini API dahulu

    Url: {http://gdkwebsvr:7021/adimobile/esign/oauth/token}
*/

const authorization = [
    [
        {
            // 0
            username      : '<EMAIL>',
            password      : 'Password123!',
            bearerToken   : 'Bearer ' + '8Qq+NmLGnmGN3HhDTpefHhuJe8o=',
            msg           : 'vvTPEYj9kRY9LGHTs+eZSzi8kaxZiKqqSbM7EjAvIy+iRqW8xomingKJje/VUxfyF2fipOlG/Bh3Spp/ossTOA=='
        },
        {
            // 1
            username      : '<PERSON><PERSON><PERSON><PERSON><PERSON>@ADINS',
            password      : 'password',
            bearerToken   : 'Bearer ' + 'LtWc1KVifr+aSVC8kyFJCWLd6d4='
        },
        {
            // 2
            username      : '<EMAIL>',
            password      : 'password',
            bearerToken   : 'Bearer ' + 'fWbjS53rGyj5YvqqiHFCPmgtKfQ='
        },
        {
            // 3
            username      : 'AD<PERSON><PERSON>@ADINS.CO.ID',
            password      : 'Password123!',
            bearerToken   : 'Bearer ' + 'd1xas4Z+pJBB5l7LHRhAz0+gN4I='
        }
    ]
];
module.exports = authorization;
