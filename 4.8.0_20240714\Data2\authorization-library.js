/*
    Setia<PERSON> mau run yang membutuhkan token, run ini API dahulu

    Url: {http://gdkwebsvr:7021/adimobile/esign/oauth/token}
*/

const authorization = 
    [
        {
            // 0
            username      : '<EMAIL>',
            password      : 'Password123!',
            bearerToken   : 'Bearer ' + 'ZaZ6XGeKpBUI31RHUgRLNUXqGwQ=',
            msg           : 'Pvkay+O8L4Wg/1CNGMLhW/S+yC6Ea0VnV5HuBhiqZ58OLQ34hshCPd3NbGINegR45hOdKC3Q8vWlMS0VGQnS1Q=='
        },
        {
            // 0
            username      : '<EMAIL>',
            password      : 'password',
            bearerToken   : 'Bearer ' + '0iT8xe3+p1V7+SP21aXu1n8NeRM=',
            msg           : 'Pvkay+O8L4Wg/1CNGMLhW/S+yC6Ea0VnV5HuBhiqZ58OLQ34hshCPd3NbGINegR45hOdKC3Q8vWlMS0VGQnS1Q=='
        },
        {
            // 1
            username      : 'HELPDES<PERSON>@ADINS',
            password      : 'password',
            bearerToken   : 'Bearer ' + 'LtWc1KVifr+aSVC8kyFJCWLd6d4=',
            msg           : 'Pvkay+O8L4Wg/1CNGMLhW/S+yC6Ea0VnV5HuBhiqZ58OLQ34hshCPd3NbGINegR45hOdKC3Q8vWlMS0VGQnS1Q=='
        },
        {
            // 2
            username      : '<EMAIL>',
            password      : 'password',
            bearerToken   : 'Bearer ' + 'KJA1yro6TpDjTR57Bnl13+q+urw=',
            msg           : 'Pvkay+O8L4Wg/1CNGMLhW/S+yC6Ea0VnV5HuBhiqZ58OLQ34hshCPd3NbGINegR45hOdKC3Q8vWlMS0VGQnS1Q=='

        },
        {
            // 3
            username      : '<EMAIL>',
            password      : 'Password123!',
            bearerToken   : 'Bearer ' + 'hiUFXNRcRMrQAh98LcCuUVY+vww=',
            msg           : 'Pvkay+O8L4Wg/1CNGMLhW/S+yC6Ea0VnV5HuBhiqZ58OLQ34hshCPd3NbGINegR45hOdKC3Q8vWlMS0VGQnS1Q=='
        }
    ];
module.exports = authorization;
