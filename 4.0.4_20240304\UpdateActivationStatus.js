import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';

import baseurl from "./Data/baseurl.js";



let baseurls = baseurl[0];
const url = baseurls.url + "/services/user/updateActivationUser";

const SLEEP_DURATION = 5;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'

        }
    };

  
    const requestbody = JSON.stringify({

        audit: { callerId:"MALVIN" },
        msg: "vpoWh8Lvi40y7vZL8OH+Vg==",
        phoneNo : "08120021173",
        password : "Password123!"
    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestbody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(response.body);




}
