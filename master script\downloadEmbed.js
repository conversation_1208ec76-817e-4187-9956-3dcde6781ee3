import http from "k6/http";
import { check, sleep } from "k6";
import baseurl from "./Data/baseurl.js";
import tenant from "./Data/tenant-library.js";


let baseurls = baseurl[0];
const url = baseurls.url + "/services/document/downloadDocumentReportEmbed";


const SLEEP_DURATION = 1;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {
    let tenantKey = tenant[0][1];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'


        }
    };

    const requestBody = JSON.stringify({
        audit : {
            callerId : "KELVIN"
        },
        completedDateEnd : null,
        completedDateStart : null,
        customerName : "",
        docType : "",
        inquiryType : "",
        isHO : "1",
        isMonitoring : true,
        msg : "+EH8s0REprgUGpj94nKz5cfrobCK/QmA6MEcXUWTzyQWAnQ/UMDw4Q0uHgr5kGanvHPmpaIHF0TWnV/48QEoOWr3Po/c/GcFlRMFqLri3cpw1vmBqkje4iheFTGflhil42Todb2e148L65Qn+7eswQ==",
        officeCode : "",
        refNumber : "",
        regionCode : "",
        requestedDateEnd : null,
        requestedDateStart : null,
        tenantCode : null,
        transactionStatus : "",
    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });
    console.log(response.body);




}
