import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import baseurl from "../data/baseurl.js";
import users from "../data/user.js";

let baseurls = baseurl[0];
const URL = baseurls.url  + "/services/embed/document/signDocument";
const SLEEP_DURATION = 1;


// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {
    let user = users[exec.vu.idInTest - 1];
 
    if (user == null) {
        user = {
        "username": ""
        , "nik": ""
        , "nohp": ""
        };
    }

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            ,'Authorization': authData.bearerToken
        }
    };

    const requestBody = JSON.stringify({

        audit: {
            callerId: "default"
        },
        email: user.username + "@ESIGNHUB.MY.ID",
        tenantCode: "ADINS",
        documentId : [user.documentId],
        
    });
    
    const response = http.post(URL, requestBody, requestHeader);

    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200
    });

    console.log([exec.vu.idInTest - 1 - 1], response.body);

}
