import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./Data/authorization-library.js"
import baseurl from "./Data/baseurl.js";
import tenant from "./Data/tenant-library.js";
import APIS from "./Data/apis.js";


let baseurls = baseurl[0];
const url = baseurls.url + "/services/user/s/updateDataSigner";
let authData = auth[3];


const SLEEP_DURATION = 1;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {
    let tenantKey = tenant[0][1];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            ,'Authorization': authData.bearerToken


        }
    };

    let apis = APIS[0][exec.vu.idInTest - 1];
 
    if (apis == null) {
        apis = {
        "username": ""
        , "nik": ""
        , "nohp": ""
        };
    }

  
    const requestBody = JSON.stringify({
        audit : {
            callerId : "KELVIN"
        },
        dateOfBirth : "",
        fullName : "",
        loginId : "<EMAIL>",
        newEmail : "",
        noKtp : "",
        noPhone : apis.nohp,
        tenantCode : "ADINS",
        vendorCode : "VIDA"

    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });
    console.log(response.body);




}
