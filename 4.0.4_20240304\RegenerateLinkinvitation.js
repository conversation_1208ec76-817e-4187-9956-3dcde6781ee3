import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import APIS from "./Data/apis.js";
import auth from "./Data/authorization-library.js";
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const url = baseurls.url  + "/services/user/s/regenerateInvitation";
const SLEEP_DURATION = 2;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '10m',
        },
    },
};

// Test scenario
export default function () {
    let auths = auth[0];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'Authorization': auths.bearerToken
        }
    };

    let apis = APIS[0][exec.vu.idInTest];
 
    if (apis == null) {
      apis = {
        "username": ""
        , "nik": ""
        , "nohp": ""
      };
    }

    let username_upper = apis.username.toUpperCase();
    let email = username_upper + "@ESIGNHUB.MY.ID";


    // Get User Profile (user/s/profiles)
    const requestBody = JSON.stringify({

        audit: { callerId: "<EMAIL>" }
        , receiverDetail: email
        , tenantCode: "ADINS"
        , vendorCode : "VIDA"


    });
    const response = http.post(url, requestBody, requestHeader);
  
    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200

    });

    console.log(email);
    console.log(response.body);

}
