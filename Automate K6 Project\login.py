import execjs
import requests
from dotenv import load_dotenv
import os

load_dotenv()

def getBearer(username, password):
    
    baseUrl = os.getenv('baseUrl')

    url = baseUrl +'oauth/token'

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:131.0) Gecko/20100101 Firefox/131.0',
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
    data = {
        'client_id': 'frontend',
        'grant_type': 'password',
        'username': username,
        'password': password
    }

    response = requests.post(url, headers=headers, data=data)
    if response.status_code == 200:
        print(response)
        return response.json().get('access_token')
    else:
        print(f"Failed to get token for {username}. Status code: {response.status_code}")
        print(url)
        return None
    
def getMsg(username):

    baseUrl = os.getenv('baseUrlSupport')

    url = baseUrl + 'encrypt'

    headers = {
        'Accept': 'application/json, */*',
        'Content-Type': 'application/json'
    }

    data = {
        "tenantCode": "ADINS",
        "officeCode": "HO",
        "email": username,
        "aesKey" : "CniQHdpCOsruzNcv",
        "menu" : "inquiry",
        "env" : "DEV"
    }

    response = requests.post(url, headers=headers, json=data)
    
    if response.status_code == 200:
        return response.json().get('encrypt_msg')
    else:
        print(f"Failed to get msg for {username}. Status code: {response.status_code}")
        return None
    
def updateLogin() :
    with open('data/auth.js', 'r') as file:
       auth = file.read()

    context = execjs.compile(auth)
    authorizations = context.eval('authorization')

    for authorization in authorizations:
        username = authorization['username']
        password = authorization['password']
        newBearerToken = getBearer(username, password)
        msg = getMsg(username)

        if newBearerToken:
            authorization['bearerToken'] = 'Bearer ' + newBearerToken
        
        if msg:
            authorization['msg'] = msg

    with open('data/auth.js', 'w') as file:
        file.write("const authorization = \n")
        file.write("    [\n")
        for authorization in authorizations:
            file.write("        {\n")
            file.write(f"            username: '{authorization['username']}',\n")
            file.write(f"            password: '{authorization['password']}',\n")
            file.write(f"            bearerToken: '{authorization['bearerToken']}',\n")
            file.write(f"            msg: '{authorization['msg']}'\n")
            file.write("        },\n")
        file.write("    ];\n")
        file.write("module.exports = authorization;\n")

def updateLoginRegisteredUser() :
    with open('data/user.js', 'r') as file:
        user = file.read()

    context = execjs.compile(user)
    users = context.eval('user')

    for user in users:
        login = user['nohp']
        
        newBearerToken = getBearer(login, 'Password123!')

        if newBearerToken:
            user['bearerToken'] = 'Bearer ' + newBearerToken

    with open('data/user.js', 'w') as file:
        file.write("const user = \n")
        file.write("    [\n")
        
        for user in users:
            file.write("        {\n")
            file.write(f"            nik: '{user['nik']}',\n")
            file.write(f"            date: '{user['date']}',\n")
            file.write(f"            username: '{user['username']}',\n")
            file.write(f"            nohp: '{user['nohp']}',\n")
            file.write(f"            msg: '{user['msg']}',\n")
            file.write(f"            otp: '{user['otp']}',\n")
            file.write(f"            documentId: '{user['documentId']}',\n")
            file.write(f"            refNumber: '{user['refNumber']}',\n")
            file.write(f"            idSigningProcessAuditTrailDetail: '{user['idSigningProcessAuditTrailDetail']}',\n")
            file.write(f"            bearerToken: '{user['bearerToken']}'\n")
            file.write("        },\n")
        
        file.write("    ];\n")
        file.write("module.exports = user;\n")

# updateLogin()