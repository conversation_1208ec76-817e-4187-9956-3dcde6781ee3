import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import tenant from './Data/tenant-library.js';
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const url = baseurls.url + "/services/external/user/sentOtpSigning";
const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 1,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {
    let tnt =  tenant[0][0]
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': tnt["xapikey"]
        }
    };

    const requestBody = JSON.stringify({

        audit: {
            callerId: "default"
        },
        phoneNo: "089654990288",
        email: "<EMAIL>",
        refNumber: "EXTSIGN-PRIVY-WOMF-0009"

    });
    
    const response = http.post(url, requestBody, requestHeader);

    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200
    });

    console.log([exec.vu.idInTest - 1], response.body);

}
