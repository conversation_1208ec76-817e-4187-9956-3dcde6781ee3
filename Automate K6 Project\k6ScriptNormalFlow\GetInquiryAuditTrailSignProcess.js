import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import users from "../data/user.js";
import baseurl from "../data/baseurl.js";
import auths from "../data/auth.js";

let baseurls = baseurl[0];
const url = baseurls.url +"/services/signingProcessAuditTrail/s/getInquiryAuditTrailSignProcess";
let auth = auths[6];

const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            ,'Authorization': auth.bearerToken
        }
    };

    let user = users[exec.vu.idInTest - 1];
 
    if (users == null) {
        users = {
        "username": ""
        , "nik": ""
        , "nohp": ""
        };
    }

    
    const requestBody = JSON.stringify({

        audit: { callerId: auth.username },
        nik : user.nik,
        refNumber : "",
        processType : "",
        tenantCode : "",
        inquiryStartDate : "",
        inquiryEndDate : ""     
        
        
    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });
    console.log(response.body);




}
