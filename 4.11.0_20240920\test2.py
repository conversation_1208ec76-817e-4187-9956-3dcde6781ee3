import execjs
import requests

def getBearer(baseurl, username, password):
    url = baseurl +'/oauth/token'
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:131.0) Gecko/20100101 Firefox/131.0',
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
    data = {
        'client_id': 'frontend',
        'grant_type': 'password',
        'username': username,
        'password': password
    }

    response = requests.post(url, headers=headers, data=data)
    if response.status_code == 200:
        return response.json().get('access_token')
    else:
        print(f"Failed to get token for {username}. Status code: {response.status_code}")
        print(url)
        return None
    
def getMsg(username):
    url = 'https://mockdata.gyandi.dev/encrypt'
    headers = {
        'Accept': 'application/json, */*',
        'Content-Type': 'application/json'
    }
    data = {
        "tenantCode": "ADINS",
        "officeCode": "HO",
        "email": username,
        "aesKey" : "CniQHdpCOsruzNcv",
        "menu" : "inquiry",
        "env" : "DEV"
    }

    response = requests.post(url, headers=headers, json=data)
    if response.status_code == 200:
        return response.json().get('encrypt_msg')
    else:
        print(f"Failed to get msg for {username}. Status code: {response.status_code}")
        return None

# Read the JavaScript code from the file
with open('Data/authorization-library.js', 'r') as file:
    js_code = file.read()

with open('Data/baseurl.js', 'r') as fileurl:
    js_code_url = fileurl.read()    

# Compile and execute JavaScript
context = execjs.compile(js_code)
authorizations = context.eval('authorization')

context = execjs.compile(js_code_url)
urls = context.eval('baseurl')

baseurl = urls[0]['url']

# Loop through each authorization and update bearerToken
for authorization in authorizations:
    username = authorization['username']
    password = authorization['password']
    newBearerToken = getBearer(baseurl, username, password)
    msg = getMsg(username)

    if newBearerToken:
        authorization['bearerToken'] = 'Bearer ' + newBearerToken
    
    if msg:
        authorization['msg'] = msg

with open('Data/authorization-library.js', 'w') as file:
    file.write("const authorization = \n")
    file.write("    [\n")
    for authorization in authorizations:
        file.write("        {\n")
        file.write(f"            username: '{authorization['username']}',\n")
        file.write(f"            password: '{authorization['password']}',\n")
        file.write(f"            bearerToken: '{authorization['bearerToken']}',\n")
        file.write(f"            msg: '{authorization['msg']}'\n")
        file.write("        },\n")
    file.write("    ];\n")
    file.write("module.exports = authorization;\n")

for index, authorization in enumerate(authorizations):
    username = authorization['username']
    password = authorization['password']
    bearer_token = authorization['bearerToken']
    msg = authorization['msg']

    print(f"Authorization {index + 1}:")
    print(f"  Username: {username}")
    print(f"  Password: {password}")
    print(f"  Bearer Token: {bearer_token}")
    print(f"  Message: {msg}")
    print()
