import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import APIS from "./Data/apis.js";
import tenant from "./Data/tenant-library.js"
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const url = baseurls.url +"/services/user/generateInvitationLink";

const SLEEP_DURATION = 5;
const USERNAME = "<EMAIL>";
const PASSWORD = "supersupeR4!";
const USERNAME_2 = "<EMAIL>";

export let options = {
  discardResponseBodies: false,
  scenarios: {

   contacts: {
      executor: 'per-vu-iterations',
      vus: 20,
      iterations: 1,
      maxDuration: '10m',
    },
  },
};

// Test scenario
export default function () {

  let tenantKey = tenant[0][1];
  const requestHeader = {
    headers: {
      'Content-Type': 'application/json'
      , 'x-api-key': 'Gy6Ka5XhgRmq@ADINS'
    }
  };

  // Random e-mail suffix

  let apis = APIS[0][exec.vu.idInTest];
 
  if (apis == null) {
    apis = {
      "username": ""
      , "nik": ""
      , "nohp": ""
    };
  }
  // Get User Profile (user/s/profiles)
  const requestBody = JSON.stringify({

    audit: { callerId: apis.username }
    , tenantCode: "ADINS"
    , users: [{
      nama: apis.username,
      tlp: apis.nohp,
      email: [apis.username]+ "@esignhub.my.id",
      jenisKelamin: "M",
      tmpLahir: "BOGOR",
      tglLahir: apis.date,
      idKtp: apis.nik,
      provinsi: "Jawa Barat",
      kota: "Bogor",
      kecamatan: "Bogor Selatan",
      kelurahan: "Baranangsiang",
      kodePos: "16143",
      alamat: "JL. SAWOKNA NO.1000 BANTAR KEMANG"
      , vendorCode: "VIDA"
    }]


  });

  const response = http.post(url, requestBody, requestHeader);
  console.log(response.body);
  sleep(SLEEP_DURATION);

  check(response, {
    'is status 200': (r) => r.status === 200 
    
  });

}
