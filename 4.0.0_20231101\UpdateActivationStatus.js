import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';

import baseurl from "./Data/baseurl.js"



let baseurls = baseurl[0]
const url = baseurls + "/services/user/updateActStatusV2";

const SLEEP_DURATION = 5;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {

    const paramRequestSigning = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'

        }
    };

  




    const requestbody = JSON.stringify({

        audit: { callerId:"MALVIN" },
        msg: "",
        tenantCode : ""
    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestbody, paramRequestSigning);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(response.body);




}
