import psycopg2
from psycopg2 import sql
from dotenv import load_dotenv
import os
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import base64
import re
import execjs
import json
import pytz
from datetime import datetime
import requests

load_dotenv()

def getAesKey():

    db_config = {
        'dbname': os.getenv('dataSourceName'),
        'user': os.getenv('dataSourceUsername'),
        'password': os.getenv('dataSourcePassword'),
        'host': os.getenv('dataSourceUrl'),
        'port': os.getenv('dataSourcePort')
    }

    try:
        connection = psycopg2.connect(**db_config)
        cursor = connection.cursor()

        query = '''
        select aes_encrypt_key from ms_tenant
        where tenant_code = 'ADINS'
        '''
        cursor.execute(query)

        result = cursor.fetchone()

        if result is None:
            aesKey = ""
        else : 
            aesKey=  result[0]
            print(aesKey)
        
        connection.commit()

    except (Exception, psycopg2.DatabaseError) as error:
        print(f"Error occurred: {error}")
    finally:
        if connection:
            cursor.close()
            connection.close()

    return aesKey
    

def encrpyt(data, aesKey):
    url = "https://mockdata.gyandi.dev/encrypt"
    
    headers = {
        "Content-Type": "application/json",
    }

    try:
        response = requests.post(url, json=data, headers=headers)       
        if response.status_code == 200:
            data = response.json()
            encrypt_msg = data.get('encrypt_msg')
            url = data.get('final_url')
            print(url)
            print(encrypt_msg)
            return encrypt_msg
        else:
            print(f"API call failed with status code {response.status_code}")
            print(response.text)
            return None

    except requests.exceptions.RequestException as e:
        print(f"Error calling API: {e}")
        return None
    
def createJson(email, aesKey):
    jakarta_tz = pytz.timezone("Asia/Jakarta")
    timestamp = datetime.now(jakarta_tz).strftime("%Y-%m-%d %H:%M:%S")

    data = {
        "tenantCode": "ADINS",
        "officeCode": "0191",
        "email": email,
        "aesKey" : aesKey,
        "menu" : "dashboard",
        "env" : "dev"
    }
    return data

def updateUserMsg():
    with open('data/user.js', 'r') as file:
        user_data = file.read()

    # Compile JavaScript context to interpret `user_data`
    context = execjs.compile(user_data)
    users = context.eval('user')  # Evaluate `user` as a JSON-like object

    for user in users:
        username = user['username']
        email = f"{username}@esignhub.my.id"

        print(email)
        
        aesKey = getAesKey()

        data = createJson(email, aesKey)

        encryptedInvitation = encrpyt(data, aesKey)

        # Add the encrypted message to `msg` field
        if encryptedInvitation:
            user['msg'] = encryptedInvitation

    # Write the updated data back to `user.js`
    with open('data/user.js', 'w') as file:
        file.write("const user = \n")
        file.write(json.dumps(users, indent=4))
        file.write(";\nmodule.exports = user;\n")


# updateUserMsg()