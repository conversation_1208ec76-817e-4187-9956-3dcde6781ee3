import http from "k6/http";
import baseurl from "./master script/Data/baseurl.js";

// Configuration
let baseurls = baseurl[0];
const loginUrl = baseurls.url + "/oauth/token";

// Test login to get fresh token
export default function () {
    const payload = {
        client_id: 'frontend',
        grant_type: 'password',
        username: '<EMAIL>',
        password: 'Password123!'
    };

    const param = {
        headers: {'Content-Type': 'application/x-www-form-urlencoded'}
    };

    console.log(`Attempting login to: ${loginUrl}`);
    console.log(`Username: ${payload.username}`);

    const response = http.post(loginUrl, payload, param);
    
    console.log(`Login response status: ${response.status}`);
    console.log(`Login response body: ${response.body}`);

    if (response.status === 200) {
        try {
            const jsonObject = response.json();
            if (jsonObject.access_token) {
                console.log(`✓ Login successful!`);
                console.log(`Access Token: ${jsonObject.access_token}`);
                console.log(`Bearer Token: Bearer ${jsonObject.access_token}`);
                console.log(`Token Type: ${jsonObject.token_type}`);
                console.log(`Expires In: ${jsonObject.expires_in} seconds`);
            } else {
                console.error(`✗ No access token in response`);
            }
        } catch (e) {
            console.error(`✗ Failed to parse JSON response: ${e.message}`);
        }
    } else {
        console.error(`✗ Login failed with status ${response.status}`);
    }
}

export let options = {
    vus: 1,
    iterations: 1,
};
