import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';

const URL = "http://localhost:8095/services/external/user/sentOtpSigning";
const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 1,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {

    const paramsSentOtpSigning = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': 'ASDFGH@WOMF'
        }
    };

    const generateSentOtpSigning = JSON.stringify({

        audit: {
            callerId: "default"
        },
        phoneNo: "089654990288",
        email: "<EMAIL>",
        refNumber: "EXTSIGN-PRIVY-WOMF-0009"

    });
    
    const checkSentOtpSigning = http.post(URL, generateSentOtpSigning, paramsSentOtpSigning);

    sleep(SLEEP_DURATION);

    check(checkSentOtpSigning, {
        'is status 200': (r) => r.status === 200
    });

    console.log([exec.vu.idInTest - 1], checkSentOtpSigning.body);

}
