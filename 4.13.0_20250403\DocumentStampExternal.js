import { check } from 'k6';
import env, {vus, iterations} from '../environment.js';
import http from 'k6/http';
import {login, getCurrentTimestampString} from '../basic-function.js';

const documentBase64 = open('/Data/document-base64.txt');

export let options = env.loadTestOptions;

export default function() {

    const currentIteration = __ITER;
    const currentVus = __VU;
    const index = (currentIteration * vus) + (currentVus - 1);

    const url = env.baseUrl + '/services/external/document/insertStampingMaterai';

    const payload = JSON.stringify({
        audit: {
            callerId: 'LOADTEST'
        },
        stampingLocations: [{
            stampPage: '1',
            notes: '',
            stampLocation: {
                llx: '0',
                lly: '0',
                urx: '130',
                ury: '130'
            }
        }],
        officeCode: 'LT_OFFICE',
        officeName: 'Load Test Office',
        documentTemplateCode: '',
        documentNumber: 'LT_' + getCurrentTimestampString() + '_' + index,
        docName: 'Load Test Document ' + index,
        docDate: '2025-04-03',
        docTypeCode: 'GENERAL',
        peruriDocTypeId: '8cf53120-3c28-496f-920f-aed7e587856e',
        taxType: 'Non Pemungut',
        docNominal: '0',
        documentFile: documentBase64
    });

    const param = {
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'x-api-key': 'Gy6Ka5XhgRmq@ADINS'
        }
    };

    const response = http.post(url, payload, param);
    const responseBody = response.json();
    
    check(response, {
        'HTTP code 200' : (resp) => resp.status === 200,
    });
    check(responseBody, {
        'Status code 0' : (body) => body.status.code === 0
    });
    
    console.log(response.body);
}