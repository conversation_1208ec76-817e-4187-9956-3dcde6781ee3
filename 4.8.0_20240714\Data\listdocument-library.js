// Ini ListDocument dari akun L<PERSON>.<EMAIL>

const listDocument = [
    [
        {
            refNumber: "TEST-VIDA-10",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-TEST2",
            customerName: "USERGDAA",
            requestDate: "2023-04-06 14:49:39",
            documentId: "00155D0B-7502-A38F-11ED-D2CA4DD7ABC0",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4142,
            totalStamped: "0/0",
            vendorCode: "VIDA",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "Lr7WbXgiHhOmvf044QT5YYixPYFAPaCeO7tnG5d1RqThGq+KB8NUYFaUV114tl54"
        },
        {
            refNumber: "TEST-VIDA-11",
            docTypeName: "<PERSON>ku<PERSON> Ko<PERSON>",
            docTemplateName: "TEST-TEST2",
            customerName: "USERGDAA",
            requestDate: "2023-04-09 06:51:02",
            documentId: "00155D0B-7502-A38F-11ED-D2CA4D8A4F10",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4153,
            totalStamped: "0/0",
            vendorCode: "VIDA",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "Lr7WbXgiHhOmvf044QT5YYixPYFAPaCeO7tnG5d1RqThGq+KB8NUYFaUV114tl54"
        },
        {
            refNumber: "TEST-VIDA-12",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-TEST2",
            customerName: "USERGDAA",
            requestDate: "2023-04-10 18:04:34",
            documentId: "00155D0B-7502-A38F-11ED-D2C92F15A300",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4156,
            totalStamped: "0/0",
            vendorCode: "VIDA",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "Lr7WbXgiHhOmvf044QT5YYixPYFAPaCeO7tnG5d1RqThGq+KB8NUYFaUV114tl54"
        },
        {
            refNumber: "TEST-VIDA-13",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-TEST2",
            customerName: "USERGDAA",
            requestDate: "2023-04-10 18:45:26",
            documentId: "00155D0B-7502-A38F-11ED-D2C92F15A300",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4157,
            totalStamped: "0/0",
            vendorCode: "VIDA",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "Lr7WbXgiHhOmvf044QT5YYixPYFAPaCeO7tnG5d1RqThGq+KB8NUYFaUV114tl54"
        },
        {
            refNumber: "TEST-VIDA-14",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-TEST2",
            customerName: "USERGDAA",
            requestDate: "2023-04-10 20:21:56",
            documentId: "00155D0B-7502-A38F-11ED-D2C92F15A300",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4158,
            totalStamped: "0/0",
            vendorCode: "VIDA",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "Lr7WbXgiHhOmvf044QT5YYixPYFAPaCeO7tnG5d1RqThGq+KB8NUYFaUV114tl54"
        },
        {
            refNumber: "TEST-VIDA-15",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-TEST2",
            customerName: "USERGDAA",
            requestDate: "2023-04-10 20:39:19",
            documentId: "00155D0B-7502-A38F-11ED-D2C92F15A300",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4159,
            totalStamped: "0/0",
            vendorCode: "VIDA",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "Lr7WbXgiHhOmvf044QT5YYixPYFAPaCeO7tnG5d1RqThGq+KB8NUYFaUV114tl54"
        },
        {
            refNumber: "TEST-SEND-SIM3-04",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "PERJANJIAN PEMBIAYAAN MASKU",
            customerName: "USERGDAA",
            requestDate: "2023-04-18 14:52:31",
            documentId: "00155D0B-7502-A38F-11ED-D2C92F15A300",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4317,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "Lr7WbXgiHhOmvf044QT5YYixPYFAPaCeO7tnG5d1RqThGq+KB8NUYFaUV114tl54"
        },
        {
            refNumber: "TEST-SEND-SIM3-04",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "PERJANJIAN PEMBIAYAAN MASKU",
            customerName: "USERGDAA",
            requestDate: "2023-04-18 14:52:55",
            documentId: "00155D0B-7502-A38F-11ED-D2C92F15A300",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4318,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Proses TTD",
            encryptDocumentByDocD: "Lr7WbXgiHhOmvf044QT5YYixPYFAPaCeO7tnG5d1RqThGq+KB8NUYFaUV114tl54"
        },
        {
            refNumber: "EXTSIGN-PRIVY-WOMF-0001",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-BE-230627-01-NAME",
            customerName: "USERGDAA",
            requestDate: "2023-07-17 13:11:51",
            documentId: "00155D0B-7502-A38F-11ED-D2C92F15A300",
            totalSigned: "0 / 2",
            signStatus: "Need Sign",
            idDocumentD: 6740,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Proses TTD",
            encryptDocumentByDocD: "Lr7WbXgiHhOmvf044QT5YYixPYFAPaCeO7tnG5d1RqThGq+KB8NUYFaUV114tl54"
        },
        {
            refNumber: "EXTSIGN-PRIVY-WOMF-0002",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-BE-230627-01-NAME",
            customerName: "USERGDAA",
            requestDate: "2023-07-17 13:12:58",
            documentId: "00155D0B-7502-A38F-11ED-D2C92F15A300",
            totalSigned: "0 / 2",
            signStatus: "Need Sign",
            idDocumentD: 6741,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "Lr7WbXgiHhOmvf044QT5YYixPYFAPaCeO7tnG5d1RqThGq+KB8NUYFaUV114tl54"
        }

        //11 kebeaaah
        ,
        {
            refNumber: "EXTSIGN-PRIVY-WOMF-0002",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-BE-230627-01-NAME",
            customerName: "USERGDAA",
            requestDate: "2023-07-17 13:12:58",
            documentId: "00155D0B-7502-A38F-11ED-D2C92F15A300",
            totalSigned: "0 / 2",
            signStatus: "Need Sign",
            idDocumentD: 6741,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "Lr7WbXgiHhOmvf044QT5YYixPYFAPaCeO7tnG5d1RqThGq+KB8NUYFaUV114tl54"
        },
        {
            refNumber: "EXTSIGN-PRIVY-WOMF-0002",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-BE-230627-01-NAME",
            customerName: "USERGDAA",
            requestDate: "2023-07-17 13:12:58",
            documentId: "00155D0B-7502-A38F-11ED-D2C92F15A300",
            totalSigned: "0 / 2",
            signStatus: "Need Sign",
            idDocumentD: 6741,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "Lr7WbXgiHhOmvf044QT5YYixPYFAPaCeO7tnG5d1RqThGq+KB8NUYFaUV114tl54"
        },
        {
            refNumber: "EXTSIGN-PRIVY-WOMF-0002",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-BE-230627-01-NAME",
            customerName: "USERGDAA",
            requestDate: "2023-07-17 13:12:58",
            documentId: "00155D0B-7502-A38F-11ED-D2C92F15A300",
            totalSigned: "0 / 2",
            signStatus: "Need Sign",
            idDocumentD: 6741,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "Lr7WbXgiHhOmvf044QT5YYixPYFAPaCeO7tnG5d1RqThGq+KB8NUYFaUV114tl54"
        },
        {
            refNumber: "EXTSIGN-PRIVY-WOMF-0002",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-BE-230627-01-NAME",
            customerName: "USERGDAA",
            requestDate: "2023-07-17 13:12:58",
            documentId: "00155D0B-7502-A38F-11ED-D2C92F15A300",
            totalSigned: "0 / 2",
            signStatus: "Need Sign",
            idDocumentD: 6741,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "Lr7WbXgiHhOmvf044QT5YYixPYFAPaCeO7tnG5d1RqThGq+KB8NUYFaUV114tl54"
        },
        {
            refNumber: "EXTSIGN-PRIVY-WOMF-0002",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-BE-230627-01-NAME",
            customerName: "USERGDAA",
            requestDate: "2023-07-17 13:12:58",
            documentId: "00155D0B-7502-A38F-11ED-D2C92F15A300",
            totalSigned: "0 / 2",
            signStatus: "Need Sign",
            idDocumentD: 6741,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "Lr7WbXgiHhOmvf044QT5YYixPYFAPaCeO7tnG5d1RqThGq+KB8NUYFaUV114tl54"
        },
        {
            refNumber: "EXTSIGN-PRIVY-WOMF-0002",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-BE-230627-01-NAME",
            customerName: "USERGDAA",
            requestDate: "2023-07-17 13:12:58",
            documentId: "00155D0B-7502-A38F-11ED-D2C92F15A300",
            totalSigned: "0 / 2",
            signStatus: "Need Sign",
            idDocumentD: 6741,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "Lr7WbXgiHhOmvf044QT5YYixPYFAPaCeO7tnG5d1RqThGq+KB8NUYFaUV114tl54"
        },
        {
            refNumber: "EXTSIGN-PRIVY-WOMF-0002",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-BE-230627-01-NAME",
            customerName: "USERGDAA",
            requestDate: "2023-07-17 13:12:58",
            documentId: "00155D0B-7502-A38F-11ED-D2C92F15A300",
            totalSigned: "0 / 2",
            signStatus: "Need Sign",
            idDocumentD: 6741,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "Lr7WbXgiHhOmvf044QT5YYixPYFAPaCeO7tnG5d1RqThGq+KB8NUYFaUV114tl54"
        },
        {
            refNumber: "EXTSIGN-PRIVY-WOMF-0002",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-BE-230627-01-NAME",
            customerName: "USERGDAA",
            requestDate: "2023-07-17 13:12:58",
            documentId: "00155D0B-7502-A38F-11ED-D2C92F15A300",
            totalSigned: "0 / 2",
            signStatus: "Need Sign",
            idDocumentD: 6741,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "Lr7WbXgiHhOmvf044QT5YYixPYFAPaCeO7tnG5d1RqThGq+KB8NUYFaUV114tl54"
        },
        {
            refNumber: "EXTSIGN-PRIVY-WOMF-0002",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-BE-230627-01-NAME",
            customerName: "USERGDAA",
            requestDate: "2023-07-17 13:12:58",
            documentId: "00155D0B-7502-A38F-11ED-D2C92F15A300",
            totalSigned: "0 / 2",
            signStatus: "Need Sign",
            idDocumentD: 6741,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "Lr7WbXgiHhOmvf044QT5YYixPYFAPaCeO7tnG5d1RqThGq+KB8NUYFaUV114tl54"
        }

    ]
];
module.exports = listDocument;