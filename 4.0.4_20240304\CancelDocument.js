import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import refNumber from "./Data/refNumber.js";
import baseurl from "./Data/baseurl.js";
import tenant from "./Data/tenant-library.js";
import auth from "./Data/authorization-library.js"


let baseurls = baseurl[0];
let authData = auth[4];

const url = baseurls.url + "/services/document/s/cancelDigitalSign";

const SLEEP_DURATION = 5;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {
    let tenantKey = tenant[0][1];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            ,'Authorization': authData.bearerToken

        }
    };

  




    const requestBody = JSON.stringify(
        {
            tenantCode:"ADINS",
            refNumber:refNumber[exec.vu.idInTest - 1],
            audit:{
                callerId:"CONFINS"
            }
        }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(response.body);




}
