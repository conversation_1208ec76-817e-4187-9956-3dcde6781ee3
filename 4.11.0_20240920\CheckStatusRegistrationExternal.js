import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import tenant from "./Data/tenant-library.js";
import baseurl from "./Data/baseurl.js";
import APIS from "./Data/registeredUser.js";

let baseurls = baseurl[0];
const url = baseurls.url  + "/services/external/user/checkRegistration";
const SLEEP_DURATION = 0;
const USERNAME = "<EMAIL>";
const PASSWORD = "supersupeR4!";
const USERNAME_2 = "<EMAIL>";


// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '10m',
        },
    },
};

// Test scenario
export default function () {
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': 'Gy6Ka5XhgRmq@ADINS'
            //  ,'Cookie' : 'idjs=FDD47A1F028E0E6900095FC1D1395777'
        }
    };

    let apis = APIS[exec.vu.idInTest-1];
 
    if (apis == null) {
        apis = {
        "username": ""
        , "nik": ""
        , "nohp": ""
        };
    }

    const requestBody = JSON.stringify({

        audit: { callerId: "UserIBAA" }
        , dataType: "NIK"
        , userData: apis.nik

    });

    const response = http.post(url, requestBody, requestHeader);
  
    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200

    });

    console.log(response.body);

}
