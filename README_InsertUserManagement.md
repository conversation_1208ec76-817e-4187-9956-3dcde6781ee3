# InsertUserManagement Load Test

Script untuk melakukan load testing insert 1 juta user menggunakan endpoint `/s/insertUserManagement` pada eSignHub.

## Overview

Script ini dibuat untuk menguji performa endpoint `insertUserManagement` dengan volume tinggi (1 juta user). Setiap user memiliki `loginId` dan `fullName` yang unik, sementara parameter lainnya tetap sama sesuai dengan spesifikasi.

## Files

### Core Files
- `generateUserData.py` - Generator untuk membuat 1 juta data user unik
- `master script/InsertUserManagement.js` - Script K6 mode high performance
- `master script/InsertUserManagementBatch.js` - Script K6 mode batch (recommended)
- `runInsertUserManagement.py` - Runner script dengan menu interaktif

### Generated Files
- `master script/Data/userManagementData.js` - Data 1 juta user (auto-generated)
- `insert_user_management_results_*.json` - Hasil load test

## Prerequisites

1. **Python 3.x** dengan module json
2. **K6** - Install dari https://k6.io/docs/getting-started/installation/
3. **Akses ke eSignHub API** dengan credentials yang valid

## Quick Start

### 1. Jalankan Runner Script
```bash
python runInsertUserManagement.py
```

### 2. Pilih Menu
- **Option 4**: Generate data + Run batch test (recommended untuk first run)
- **Option 2**: Run batch test (jika data sudah ada)

### 3. Manual Steps (Alternative)

#### Generate Data
```bash
python generateUserData.py
```

#### Run Load Test
```bash
# Batch mode (recommended)
k6 run "master script/InsertUserManagementBatch.js"

# High performance mode
k6 run "master script/InsertUserManagement.js"
```

## Configuration

### Request Parameters
Berdasarkan cURL yang diberikan:
```json
{
    "audit": {
        "callerId": "<EMAIL>"
    },
    "fullName": "[UNIQUE_FULL_NAME]",
    "tenantCode": "ADINS",
    "loginId": "[UNIQUE_LOGIN_ID]",
    "roleCode": "ADMCLIENT",
    "password": "ADINS",
    "officeCode": "0071"
}
```

### Unique Data Generation
- **loginId**: `USERMANAGEMENT.{PREFIX}{INDEX}@TEST.COM`
  - Contoh: `<EMAIL>`, `<EMAIL>`
- **fullName**: Kombinasi nama depan + nama belakang + suffix (jika perlu)
  - Contoh: `Jeremy Smith`, `Christopher Johnson`, `Michael Williams 1`

### Load Test Modes

#### Batch Mode (Recommended)
- **VUs**: 50 virtual users
- **Iterations**: 20,000 per VU
- **Total**: 1,000,000 requests
- **Duration**: ~3 hours
- **Features**: Retry logic, error handling, conservative rate

#### High Performance Mode
- **VUs**: 100 virtual users  
- **Iterations**: 10,000 per VU
- **Total**: 1,000,000 requests
- **Duration**: ~2 hours
- **Features**: Higher throughput, minimal delays

## Monitoring

### Progress Logging
- Progress ditampilkan setiap 50 requests (batch) atau 100 requests (high perf)
- Format: `Progress: User X/1000000 (Y%) - LoginId: Z, Status: 200`

### Error Handling
- Automatic retry untuk server errors (5xx) dan rate limiting (429)
- Exponential backoff untuk retry
- Detailed error logging untuk debugging

### Metrics
K6 akan menghasilkan metrics standar:
- `http_req_duration` - Response time
- `http_req_failed` - Error rate
- `iterations` - Completed requests
- `vus` - Virtual users

## Expected Results

### Success Response
```json
{
    "status": {
        "code": 0,
        "message": "Success"
    }
}
```

### Common Errors
1. **User ID Already Exist** - LoginId sudah ada di database
2. **Role Not Exist** - Role code tidak valid
3. **Office Not Available** - Office code tidak valid
4. **Tenant Not Found** - Tenant tidak ditemukan

## Performance Considerations

### Server Impact
- Test ini akan membuat 1 juta record di database
- Pastikan server memiliki kapasitas yang cukup
- Monitor CPU, memory, dan disk space selama test

### Network
- Total data transfer: ~500MB (request + response)
- Bandwidth requirement: ~1-2 Mbps sustained

### Database
- Estimasi storage: ~100MB untuk 1 juta user records
- Index performance akan terpengaruh
- Backup database sebelum test

## Troubleshooting

### Common Issues

1. **"User data not found"**
   - Jalankan `python generateUserData.py` terlebih dahulu

2. **"K6 not found"**
   - Install K6: https://k6.io/docs/getting-started/installation/

3. **"Authorization failed"**
   - Update token di `master script/Data/authorization-library.js`

4. **"Rate limiting (429)"**
   - Script akan otomatis retry dengan backoff
   - Pertimbangkan mengurangi VUs

5. **"Server errors (5xx)"**
   - Check server capacity dan health
   - Pertimbangkan menggunakan batch mode

### Performance Tuning

1. **Reduce VUs** jika server overload
2. **Increase sleep duration** untuk mengurangi load
3. **Use batch mode** untuk stability
4. **Monitor server resources** selama test

## Results Analysis

### K6 Summary
Setelah test selesai, K6 akan menampilkan summary:
- Total requests sent
- Success rate
- Average response time
- Error breakdown

### JSON Output
Detailed results tersimpan di file JSON untuk analisis lebih lanjut:
```bash
# View summary
jq '.metrics' insert_user_management_results_*.json

# View error details
jq '.root_group.checks[] | select(.passes == 0)' insert_user_management_results_*.json
```

## Cleanup

Setelah test selesai:
1. Review hasil dan error logs
2. Cleanup test data jika diperlukan
3. Archive hasil test untuk referensi
4. Reset server state jika perlu

## Support

Untuk pertanyaan atau issues:
1. Check troubleshooting section
2. Review K6 documentation
3. Check server logs untuk error details
4. Contact development team jika ada business logic issues
