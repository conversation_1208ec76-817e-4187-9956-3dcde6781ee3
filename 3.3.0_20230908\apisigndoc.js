import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./Data/authorization-library.js";
import baseurl from "./Data/baseurl.js"

let baseurls = baseurl[0]
const URL = baseurls.url  + "/services/document/s/signConfirmDokumen";
const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {
    let auths = auth[0][0];
    const paramsSignDoc = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'Authorization': auths['bearerToken']
        }
    };

    const generateSignDoc = JSON.stringify({

        audit: {
            callerId: "default"
        },
        email: "<EMAIL>",
        ipAddress: "************",
        browser: "mozila",
        documentId: [
            "00155D0B-7502-A38F-11ED-D2C92F15A300"
        ]
        
    });
    
    const checkSignDoc = http.post(URL, generateSignDoc, paramsSignDoc);

    sleep(SLEEP_DURATION);

    check(checkSignDoc, {
        'is status 200': (r) => r.status === 200
    });

    console.log([exec.vu.idInTest - 1], checkSignDoc.body);

}
