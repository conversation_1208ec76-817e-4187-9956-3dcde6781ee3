// Ini ListDocument dari akun L<PERSON>.<EMAIL>

const listDocument = [
    [
        {
            refNumber: "TEST-VIDA-10",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-TEST2",
            customerName: "USERGDAA",
            requestDate: "2023-04-06 14:49:39",
            documentId: "02426E55-4C67-9FB5-11EE-41A94C5DACB0",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4142,
            totalStamped: "0/0",
            vendorCode: "VIDA",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "yEkRH/DEP7MxQahPkXiFepQfJJFlYPErrL+FnZKd4hsmOFVTMQTCL//ii0TBKpqe"
        },
        {
            refNumber: "TEST-VIDA-11",
            docTypeName: "<PERSON>ku<PERSON> Ko<PERSON>",
            docTemplateName: "TEST-TEST2",
            customerName: "USERGDAA",
            requestDate: "2023-04-09 06:51:02",
            documentId: "02426E55-4C67-9FB5-11EE-41A94C5DACB0",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4153,
            totalStamped: "0/0",
            vendorCode: "VIDA",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "yEkRH/DEP7MxQahPkXiFepQfJJFlYPErrL+FnZKd4hsmOFVTMQTCL//ii0TBKpqe"
        },
        {
            refNumber: "TEST-VIDA-12",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-TEST2",
            customerName: "USERGDAA",
            requestDate: "2023-04-10 18:04:34",
            documentId: "02426E55-4C67-9FB5-11EE-41A94C5DACB0",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4156,
            totalStamped: "0/0",
            vendorCode: "VIDA",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "yEkRH/DEP7MxQahPkXiFepQfJJFlYPErrL+FnZKd4hsmOFVTMQTCL//ii0TBKpqe"
        },
        {
            refNumber: "TEST-VIDA-13",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-TEST2",
            customerName: "USERGDAA",
            requestDate: "2023-04-10 18:45:26",
            documentId: "02426E55-4C67-9FB5-11EE-41A94C5DACB0",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4157,
            totalStamped: "0/0",
            vendorCode: "VIDA",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "yEkRH/DEP7MxQahPkXiFepQfJJFlYPErrL+FnZKd4hsmOFVTMQTCL//ii0TBKpqe"
        },
        {
            refNumber: "TEST-VIDA-14",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-TEST2",
            customerName: "USERGDAA",
            requestDate: "2023-04-10 20:21:56",
            documentId: "02426E55-4C67-9FB5-11EE-41A94C5DACB0",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4158,
            totalStamped: "0/0",
            vendorCode: "VIDA",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "yEkRH/DEP7MxQahPkXiFepQfJJFlYPErrL+FnZKd4hsmOFVTMQTCL//ii0TBKpqe"
        },
        {
            refNumber: "TEST-VIDA-15",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-TEST2",
            customerName: "USERGDAA",
            requestDate: "2023-04-10 20:39:19",
            documentId: "02426E55-4C67-9FB5-11EE-41A94C5DACB0",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4159,
            totalStamped: "0/0",
            vendorCode: "VIDA",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "yEkRH/DEP7MxQahPkXiFepQfJJFlYPErrL+FnZKd4hsmOFVTMQTCL//ii0TBKpqe"
        },
        {
            refNumber: "TEST-SEND-SIM3-04",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "PERJANJIAN PEMBIAYAAN MASKU",
            customerName: "USERGDAA",
            requestDate: "2023-04-18 14:52:31",
            documentId: "02426E55-4C67-9FB5-11EE-41A94C5DACB0",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4317,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "yEkRH/DEP7MxQahPkXiFepQfJJFlYPErrL+FnZKd4hsmOFVTMQTCL//ii0TBKpqe"
        },
        {
            refNumber: "TEST-SEND-SIM3-04",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "PERJANJIAN PEMBIAYAAN MASKU",
            customerName: "USERGDAA",
            requestDate: "2023-04-18 14:52:55",
            documentId: "02426E55-4C67-9FB5-11EE-41A94C5DACB0",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4318,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Proses TTD",
            encryptDocumentByDocD: "yEkRH/DEP7MxQahPkXiFepQfJJFlYPErrL+FnZKd4hsmOFVTMQTCL//ii0TBKpqe"
        },
        {
            refNumber: "EXTSIGN-PRIVY-WOMF-0001",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-BE-230627-01-NAME",
            customerName: "USERGDAA",
            requestDate: "2023-07-17 13:11:51",
            documentId: "02426E55-4C67-9FB5-11EE-41A94C5DACB0",
            totalSigned: "0 / 2",
            signStatus: "Need Sign",
            idDocumentD: 6740,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Proses TTD",
            encryptDocumentByDocD: "yEkRH/DEP7MxQahPkXiFepQfJJFlYPErrL+FnZKd4hsmOFVTMQTCL//ii0TBKpqe"
        },
        {
            refNumber: "EXTSIGN-PRIVY-WOMF-0002",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-BE-230627-01-NAME",
            customerName: "USERGDAA",
            requestDate: "2023-07-17 13:12:58",
            documentId: "02426E55-4C67-9FB5-11EE-41A94C5DACB0",
            totalSigned: "0 / 2",
            signStatus: "Need Sign",
            idDocumentD: 6741,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "yEkRH/DEP7MxQahPkXiFepQfJJFlYPErrL+FnZKd4hsmOFVTMQTCL//ii0TBKpqe"
        },
        {
            refNumber: "TEST-VIDA-10",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-TEST2",
            customerName: "USERGDAA",
            requestDate: "2023-04-06 14:49:39",
            documentId: "02426E55-4C67-9FB5-11EE-41A94C5DACB0",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4142,
            totalStamped: "0/0",
            vendorCode: "VIDA",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "yEkRH/DEP7MxQahPkXiFepQfJJFlYPErrL+FnZKd4hsmOFVTMQTCL//ii0TBKpqe"
        },
        {
            refNumber: "TEST-VIDA-11",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-TEST2",
            customerName: "USERGDAA",
            requestDate: "2023-04-09 06:51:02",
            documentId: "02426E55-4C67-9FB5-11EE-41A94C5DACB0",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4153,
            totalStamped: "0/0",
            vendorCode: "VIDA",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "yEkRH/DEP7MxQahPkXiFepQfJJFlYPErrL+FnZKd4hsmOFVTMQTCL//ii0TBKpqe"
        },
        {
            refNumber: "TEST-VIDA-12",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-TEST2",
            customerName: "USERGDAA",
            requestDate: "2023-04-10 18:04:34",
            documentId: "02426E55-4C67-9FB5-11EE-41A94C5DACB0",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4156,
            totalStamped: "0/0",
            vendorCode: "VIDA",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "yEkRH/DEP7MxQahPkXiFepQfJJFlYPErrL+FnZKd4hsmOFVTMQTCL//ii0TBKpqe"
        },
        {
            refNumber: "TEST-VIDA-13",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-TEST2",
            customerName: "USERGDAA",
            requestDate: "2023-04-10 18:45:26",
            documentId: "02426E55-4C67-9FB5-11EE-41A94C5DACB0",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4157,
            totalStamped: "0/0",
            vendorCode: "VIDA",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "yEkRH/DEP7MxQahPkXiFepQfJJFlYPErrL+FnZKd4hsmOFVTMQTCL//ii0TBKpqe"
        },
        {
            refNumber: "TEST-VIDA-14",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-TEST2",
            customerName: "USERGDAA",
            requestDate: "2023-04-10 20:21:56",
            documentId: "02426E55-4C67-9FB5-11EE-41A94C5DACB0",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4158,
            totalStamped: "0/0",
            vendorCode: "VIDA",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "yEkRH/DEP7MxQahPkXiFepQfJJFlYPErrL+FnZKd4hsmOFVTMQTCL//ii0TBKpqe"
        },
        {
            refNumber: "TEST-VIDA-15",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-TEST2",
            customerName: "USERGDAA",
            requestDate: "2023-04-10 20:39:19",
            documentId: "02426E55-4C67-9FB5-11EE-41A94C5DACB0",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4159,
            totalStamped: "0/0",
            vendorCode: "VIDA",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "yEkRH/DEP7MxQahPkXiFepQfJJFlYPErrL+FnZKd4hsmOFVTMQTCL//ii0TBKpqe"
        },
        {
            refNumber: "TEST-SEND-SIM3-04",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "PERJANJIAN PEMBIAYAAN MASKU",
            customerName: "USERGDAA",
            requestDate: "2023-04-18 14:52:31",
            documentId: "02426E55-4C67-9FB5-11EE-41A94C5DACB0",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4317,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "yEkRH/DEP7MxQahPkXiFepQfJJFlYPErrL+FnZKd4hsmOFVTMQTCL//ii0TBKpqe"
        },
        {
            refNumber: "TEST-SEND-SIM3-04",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "PERJANJIAN PEMBIAYAAN MASKU",
            customerName: "USERGDAA",
            requestDate: "2023-04-18 14:52:55",
            documentId: "02426E55-4C67-9FB5-11EE-41A94C5DACB0",
            totalSigned: "1 / 2",
            signStatus: "Need Sign",
            idDocumentD: 4318,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Proses TTD",
            encryptDocumentByDocD: "yEkRH/DEP7MxQahPkXiFepQfJJFlYPErrL+FnZKd4hsmOFVTMQTCL//ii0TBKpqe"
        },
        {
            refNumber: "EXTSIGN-PRIVY-WOMF-0001",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-BE-230627-01-NAME",
            customerName: "USERGDAA",
            requestDate: "2023-07-17 13:11:51",
            documentId: "02426E55-4C67-9FB5-11EE-41A94C5DACB0",
            totalSigned: "0 / 2",
            signStatus: "Need Sign",
            idDocumentD: 6740,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Proses TTD",
            encryptDocumentByDocD: "yEkRH/DEP7MxQahPkXiFepQfJJFlYPErrL+FnZKd4hsmOFVTMQTCL//ii0TBKpqe"
        },
        {
            refNumber: "EXTSIGN-PRIVY-WOMF-0002",
            docTypeName: "Dokumen Kontrak",
            docTemplateName: "TEST-BE-230627-01-NAME",
            customerName: "USERGDAA",
            requestDate: "2023-07-17 13:12:58",
            documentId: "02426E55-4C67-9FB5-11EE-41A94C5DACB0",
            totalSigned: "0 / 2",
            signStatus: "Need Sign",
            idDocumentD: 6741,
            totalStamped: "0/2",
            vendorCode: "PRIVY",
            signingProcess: "Belum TTD",
            encryptDocumentByDocD: "yEkRH/DEP7MxQahPkXiFepQfJJFlYPErrL+FnZKd4hsmOFVTMQTCL//ii0TBKpqe"
        }
    ]
];
module.exports = listDocument;