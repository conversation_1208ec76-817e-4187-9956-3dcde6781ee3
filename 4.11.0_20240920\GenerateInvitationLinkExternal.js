import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import baseurl from "./Data/baseurl.js";
import APIS from "./Data/unRegisterUser.js";

let baseurls = baseurl[0];
const url = baseurls + "/external/user/generateInvLink";
const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {
    let apis = APIS[exec.vu.idInTest-1];
 
    if (apis == null) {
        apis = {
        "username": ""
        , "nik": ""
        , "nohp": ""
        };
    }

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': 'Gy6Ka5XhgRmq@ADINS'
        }
    };

    const requestBody = JSON.stringify({

        audit: {
            callerId: "default"
        },
        alamat : "ANCOL SELATAN",
        email : [apis.username]+ "@esignhub.my.id",
        idKtp : apis.nik,
        jenisKelamin : "M",
        kecamatan : "Tanjung Priok",
        kelurahan : "Sunter Agung",
        kodePos : "14350",
        kota : "JAKARTA UTARA",
        nama : apis.username,
        provinsi : "DKI JAKARTA",
        tglLahir : apis.date,
        tlp : apis.nohp,
        tmpLahir : "TANGERANG",
        type : "CUST"


    });
    
    const response = http.post(url, requestBody, requestHeader);

    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200
    });

    console.log([exec.vu.idInTest - 1], response.body);

}
