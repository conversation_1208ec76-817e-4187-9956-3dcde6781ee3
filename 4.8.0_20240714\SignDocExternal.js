import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./Data/authorization-library.js";
// import listDocument from "./Data/listdocument-library.js";
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const URL = baseurls.url  +  "/services/external/document/signDocument";

const SLEEP_DURATION = 1;


// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};



const listDOc = [
    "00163E03-D29C-ACD9-11EF-46680D4DCB80",
    "00163E03-D29C-ACD9-11EF-46680D50FFD0",
    "00163E03-D29C-ACD9-11EF-46680D4D7D60",
    "00163E03-D29C-ACD9-11EF-46680D4ABE40",
    "00163E03-D29C-ACD9-11EF-46680D369A00",
    "00163E03-D29C-ACD9-11EF-46680D342900",
    "00163E03-D29C-ACD9-11EF-46680D2C39C0",
    "00163E03-D29C-ACD9-11EF-46680D420BB0",
    "00163E03-D29C-ACD9-11EF-46680D4DF290",
    "00163E03-D29C-ACD9-11EF-46680D3C8D70",
    "00163E03-D29C-ACD9-11EF-46680D3C1840",
    "00163E03-D29C-ACD9-11EF-46680D40FA40",
    "00163E03-D29C-ACD9-11EF-46680D3D29B0",
    "00163E03-D29C-ACD9-11EF-46680D292C80",
    "00163E03-FD4A-A65C-11EF-46680CCB7C20",
    "00163E03-FD4A-A65C-11EF-46680CCE1430",
    "00163E03-FD4A-A65C-11EF-46680CD16F90",
    "00163E03-FD4A-A65C-11EF-46680CCCB4A0",
    "00163E03-D29C-ACD9-11EF-46680D202BD0",
    "00163E03-D29C-ACD9-11EF-46680D4B8190",
];

// Test scenario
export default function () {

    let auths = auth[0][0];
    // let listDoc = listDocument[0][exec.vu.idInInstance - 1];

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': 'Gy6Ka5XhgRmq@ADINS'
        }
    };

    const requestBody = JSON.stringify({

        
        audit: {
            callerId: "default"
        },
        documentId: [listDOc[exec.vu.idInInstance-1]]
        ,email : "<EMAIL>"
        ,password : "Password123!"
        ,ipAddress : "***********"
        , browserInfo : "Browser Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
        ,otp : "999999"
        ,selfPhoto : ""

    });
    
    const response = http.post(URL, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200
    });

    console.log(response.body);

}
