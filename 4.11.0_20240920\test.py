import execjs
import requests
    
def encrypt(docId):
    url = 'https://mockdata.gyandi.dev/encrypt_documentId'

    headers = {
        'Accept': 'application/json, */*',
        'Content-Type': 'application/json'
    }

    data = {
        "documentId": docId,
        "aesKey": "CniQHdpCOsruzNcv"
    }

    response = requests.post(url, headers=headers, json=data)
    if response.status_code == 200:
        return response.json().get('encrypt_msg')
    else:
        print(f"Failed to get msg for {docId}. Status code: {response.status_code}")
        return None

# Load JS file and retrieve document list
with open('Data/unsignDocumentId.js', 'r') as file:
    js_code = file.read()

with open('Data/DocumentId.js', 'r') as file:
    js_code_signed = file.read()

context = execjs.compile(js_code)
unsignDocuments = context.eval('encryptedDocId')

context = execjs.compile(js_code_signed)
signedDocuments = context.eval('signDoc')

for unsignDocument in unsignDocuments:
    documentId = unsignDocument['docId']
    msg = encrypt(documentId)
    
    if msg:
        unsignDocument['encryptedDocId'] = msg

for signedDocument in signedDocuments:
    documentId = signedDocument['docId']
    msg = encrypt(documentId)
    
    if msg:
        signedDocument['encryptedDocId'] = msg

with open('Data/unsignDocumentId.js', 'w') as file:
    file.write("const encryptedDocId = \n")
    file.write("    [\n")
    for unsignDocument in unsignDocuments:
        file.write("        {\n")
        file.write(f"            refNumber: '{unsignDocument['refNumber']}',\n")
        file.write(f"            docId: '{unsignDocument['docId']}',\n")
        file.write(f"            encryptedDocId: '{unsignDocument['encryptedDocId']}'\n")
        file.write("        },\n")
    file.write("    ];\n")
    file.write("module.exports = encryptedDocId;\n")

with open('Data/DocumentId.js', 'w') as file:
    file.write("const signDoc = \n")
    file.write("    [\n")
    for signedDocument in signedDocuments:
        file.write("        {\n")
        file.write(f"            refNumber: '{signedDocument['refNumber']}',\n")
        file.write(f"            docId: '{signedDocument['docId']}',\n")
        file.write(f"            encryptedDocId: '{signedDocument['encryptedDocId']}'\n")
        file.write("        },\n")
    file.write("    ];\n")
    file.write("module.exports = signDoc;\n")

for index, unsignDocument in enumerate(unsignDocuments):
    refNumber = unsignDocument['refNumber']
    documentId = unsignDocument['docId']
    msg = unsignDocument['encryptedDocId']

    print(f"unsignDocument {index + 1}:")
    print(f"  refNumber: {refNumber}")
    print(f"  documentId: {documentId}")
    print(f"  encryptedDocId: {msg}")
    print()

for index, signedDocument in enumerate(signedDocuments):
    refNumber = signedDocument['refNumber']
    documentId = signedDocument['docId']
    msg = signedDocument['encryptedDocId']

    print(f"unsignDocument {index + 1}:")
    print(f"  refNumber: {refNumber}")
    print(f"  documentId: {documentId}")
    print(f"  encryptedDocId: {msg}")
    print()
