import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./Data/authorization-library.js";

// const URL = "http://localhost:8095/services/user/s/checkRegisterAutoFill";
const URL = "https://esignhub.docsol.id:543/adimobile/esign/services/user/s/checkRegisterAutoFill";

const SLEEP_DURATION = 3;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {

    let auths = auth[0][2];

    const paramsCheckRegisterAutoFill = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'Authorization': auths['bearerToken']
        }
    };

    const generateCheckRegisterAutoFill = JSON.stringify({

        audit: {
            callerId: "default"
        },
        checkRegisterDetail: "<EMAIL>",
        tenantCode: "WOMF",
        vendorCode: "VIDA"

    });
    
    const checkCheckRegisterAutoFill = http.post(URL, generateCheckRegisterAutoFill, paramsCheckRegisterAutoFill);

    sleep(SLEEP_DURATION);

    check(checkCheckRegisterAutoFill, {
        'is status 200': (r) => r.status === 200
    });

    
    console.log([exec.vu.idInTest - 1], checkCheckRegisterAutoFill.body);

}
