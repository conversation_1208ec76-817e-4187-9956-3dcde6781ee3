import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';

import auth from "./Data/authorization-library.js";
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const url = baseurls.url + "/services/embed/user/getSignerDetailEmbed";
const SLEEP_DURATION = 2;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '10m',
        },
    },
};

// Test scenario
export default function () {
    let auths = auth[0][0];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
        }
    };


    // Get User Profile (user/s/profiles)
    const requestBody = JSON.stringify({

        audit: { callerId: "<EMAIL>" }
        , msg: "zRGQaG59B8dHnobnG0PoHsJHcHXJwITv/Qjw4D5ypM6pWtHWzl2/feN8WO9NlTIfTcm0GKYeE3/CkzuYnn8SMT/dk9QBdDQJAoslPcoqYOKmEKpOLq/4KXIe4gDsheJ3fsHqSQX7+dVhiAfYnO7WpQ=="
        , vendorCode: "VIDA"
        , tenantCode: "ADINS"

    });
    const response = http.post(url, requestBody, requestHeader);
  
    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200

    });

    console.log(response.body);

}
