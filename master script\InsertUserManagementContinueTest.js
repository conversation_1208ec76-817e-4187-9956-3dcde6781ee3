import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import userManagementData from "./Data/userManagementData.js";
import baseurl from "./Data/baseurl.js";
import authorization from "./Data/authorization-library.js";

// Configuration
let baseurls = baseurl[0];
const url = baseurls.url + "/services/user-management/s/insertUserManagement";
const SLEEP_DURATION = 0.5;

// Use <EMAIL> for authorization (index 3)
const authData = authorization[3];

// Test continuation from index 167500 - small test with 10 users
const START_INDEX = 167500;
const TEST_USERS = 10;

// Small test configuration
export let options = {
    discardResponseBodies: false,
    scenarios: {
        insert_users_continue_test: {
            executor: 'shared-iterations',
            vus: 2,            // 2 virtual users
            iterations: TEST_USERS, // 10 test iterations
            maxDuration: '5m', // 5 minutes max duration
        },
    },
    thresholds: {
        http_req_duration: ['p(95)<10000'], // 95% of requests should be below 10s
        http_req_failed: ['rate<0.2'],      // Error rate should be below 20% for testing
    },
};

// Test scenario
export default function () {
    // Use scenario iteration counter + start index
    const globalIndex = START_INDEX + exec.scenario.iterationInInstance;
    
    // Safety check: ensure index is within bounds
    if (globalIndex >= userManagementData.totalUsers) {
        console.error(`Index ${globalIndex} exceeds available data (${userManagementData.totalUsers}). Skipping.`);
        return;
    }
    
    // Get user data by global index
    const userData = userManagementData.getUserByIndex(globalIndex);
    
    if (!userData) {
        console.error(`No user data found for index ${globalIndex}`);
        return;
    }

    console.log(`Processing user ${globalIndex + 1}: LoginId=${userData.loginId}, FullName=${userData.fullName}`);

    // Request headers
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': authData.bearerToken
        }
    };

    // Request body with correct configuration
    const requestBody = JSON.stringify({
        "audit": {
            "callerId": "<EMAIL>"
        },
        "fullName": userData.fullName,
        "tenantCode": "ADINS",
        "loginId": userData.loginId,
        "roleCode": "USER_EDITOR",
        "password": "ADINS",
        "officeCode": "0191"
    });

    // Add delay to avoid overwhelming the server
    sleep(SLEEP_DURATION);

    // Make the request
    const response = http.post(url, requestBody, requestHeader);

    // Check response
    const isSuccess = check(response, {
        'is status 200': (r) => r.status === 200,
        'response has success status': (r) => {
            try {
                const body = JSON.parse(r.body);
                return body.status && body.status.code === 0;
            } catch (e) {
                return false;
            }
        }
    });

    // Log results
    if (isSuccess) {
        console.log(`✓ Successfully inserted user ${globalIndex + 1} (${userData.loginId})`);
    } else {
        console.error(`✗ Failed to insert user ${globalIndex + 1} (${userData.loginId}): Status ${response.status}`);
        
        // Try to parse error message
        try {
            const errorBody = JSON.parse(response.body);
            if (errorBody.status && errorBody.status.message) {
                console.log(`Error: ${errorBody.status.message}`);
            }
        } catch (e) {
            // Ignore JSON parse errors
        }
    }
}

// Setup function
export function setup() {
    console.log("=== InsertUserManagement Continue Test Setup ===");
    console.log(`Target URL: ${url}`);
    console.log(`Starting from index: ${START_INDEX}`);
    console.log(`Test users: ${TEST_USERS}`);
    console.log(`Total available users: ${userManagementData.totalUsers}`);
    console.log(`Authorization: ${authData.username}`);
    console.log(`VUs: 2`);
    console.log(`Total iterations: ${TEST_USERS}`);
    console.log(`Executor: shared-iterations (continue test mode)`);
    
    // Test data access
    console.log("\nTesting data access...");
    for (let i = START_INDEX; i < START_INDEX + 3; i++) {
        const testUser = userManagementData.getUserByIndex(i);
        if (testUser) {
            console.log(`  User ${i}: LoginId=${testUser.loginId}, FullName=${testUser.fullName}`);
        } else {
            console.error(`  User ${i}: No data found`);
        }
    }
    
    console.log("Setup complete. Starting continuation test...");
    return {};
}

// Teardown function
export function teardown(data) {
    console.log("=== InsertUserManagement Continue Test Complete ===");
    console.log("This was a small continuation test. Check results before running full continuation.");
}
