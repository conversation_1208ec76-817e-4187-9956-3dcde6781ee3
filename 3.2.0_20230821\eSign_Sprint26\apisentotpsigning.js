import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./Data/authorization-library.js";
const URL = "https://esignhub.docsol.id:543/adimobile/esign/services/external/user/sentOtpSigning";
const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {

    const paramsSentOtpSigning = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': 'ASDFGH@WOMF'
        }
    };

    const generateSentOtpSigning = JSON.stringify({

        audit: {
            callerId: "default"
        },
        phoneNo: "089654990285",
        email: "<EMAIL>",
        refNumber: "malvin-0001-" + exec.vu.idInInstance

    });
    
    const checkSentOtpSigning = http.post(URL, generateSentOtpSigning, paramsSentOtpSigning);

    sleep(SLEEP_DURATION);

    check(checkSentOtpSigning, {
        'is status 200': (r) => r.status === 200
    });

    console.log([exec.vu.idInTest - 1], checkSentOtpSigning.body);

}
