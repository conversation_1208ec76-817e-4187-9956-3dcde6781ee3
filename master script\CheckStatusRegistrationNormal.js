import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./Data/authorization-library.js"
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const url = baseurls.url  + "/services/user/s/checkRegisterStatus";
const SLEEP_DURATION = 1;
let authData = auth[4];


// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 2,
            maxDuration: '10m',
        },
    },
};

// Test scenario
export default function () {
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            ,'Authorization': authData.bearerToken
        }
    };

    const requestBody = JSON.stringify({

        audit: { callerId: "UserIBAA" }
        , email: "<EMAIL>"
        , phone: "081297723819"
        , vendorCode : "VIDA"
        , tenantCode : "ADINS"

    });

    const response = http.post(url, requestBody, requestHeader);
  
    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200

    });

    console.log(response.body);

}
