import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import APIS from "./Data/apis.js";
import tenant from "./Data/tenant-library.js";
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const url = baseurls.url + "/services/user/generateInvitationLink";

const SLEEP_DURATION = 5;
const USERNAME = "<EMAIL>";
const PASSWORD = "supersupeR4!";
const USERNAME_2 = "<EMAIL>";

export let options = {
  discardResponseBodies: false,
  scenarios: {
    contacts: {
      executor: 'per-vu-iterations',
      vus: 20,
      iterations: 1,
      maxDuration: '10m',
    },
  },
};

// Global array to store results
let results = [];

// Test scenario
export default function () {
  let tenantKey = tenant[0][1];
  const requestHeader = {
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': 'Gy6Ka5XhgRmq@ADINS'
    }
  };

  // Random e-mail suffix
  let apis = APIS[0][exec.vu.idInTest];
  if (apis == null) {
    apis = {
      "username": "",
      "nik": "",
      "nohp": ""
    };
  }

  // Prepare request body
  const requestBody = JSON.stringify({
    audit: { callerId: apis.username },
    tenantCode: "ADINS",
    users: [{
      nama: apis.username,
      tlp: apis.nohp,
      email: `${apis.username}@esignhub.my.id`,
      jenisKelamin: "M",
      tmpLahir: "BOGOR",
      tglLahir: apis.date,
      idKtp: apis.nik,
      provinsi: "Jawa Barat",
      kota: "Bogor",
      kecamatan: "Bogor Selatan",
      kelurahan: "Baranangsiang",
      kodePos: "16143",
      alamat: "JL. SAWOKNA NO.1000 BANTAR KEMANG",
      vendorCode: "VIDA"
    }]
  });

  // Make the HTTP POST request
  const response = http.post(url, requestBody, requestHeader);
  sleep(SLEEP_DURATION);

  // Check response status
  check(response, {
    'is status 200': (r) => r.status === 200
  });

  // Log the response body and nik
  
  console.log(response.json("links"));
  console.log(apis.nik);
}

// Handle the end of the test to save results to a file
export function handleSummary(data) {
  const output = JSON.stringify(results, null, 2);
  const filepath = __ENV.OUTPUT_FILE || 'output.json'; // Use environment variable or default to 'output.json'
  console.log(`Writing results to ${filepath}`);
  
  // Return the output in the format required by K6 to write to file
  return {
    [filepath]: output,
    stdout: output, // This will print the JSON to stdout for debugging
  };
}

