import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import userManagementData from "./Data/userManagementData.js";
import baseurl from "./Data/baseurl.js";
import authorization from "./Data/authorization-library.js";

// Configuration
let baseurls = baseurl[0];
const url = baseurls.url + "/services/user-management/s/insertUserManagement";
const SLEEP_DURATION = 0.1; // Reduced sleep for higher throughput

// Use <EMAIL> for authorization (index 3)
const authData = authorization[3];

// Load test configuration for 1 million users
export let options = {
    discardResponseBodies: false,
    scenarios: {
        insert_users: {
            executor: 'per-vu-iterations',
            vus: 100,           // 100 virtual users
            iterations: 10000,  // 10,000 iterations per VU = 1,000,000 total
            maxDuration: '120m', // 2 hours max duration
        },
    },
    thresholds: {
        http_req_duration: ['p(95)<5000'], // 95% of requests should be below 5s
        http_req_failed: ['rate<0.1'],     // Error rate should be below 10%
    },
};

// Test scenario
export default function () {
    // Calculate global user index
    const globalIndex = (exec.vu.idInTest - 1) * 10000 + exec.scenario.iterationInTest;
    
    // Get user data by global index
    const userData = userManagementData.getUserByIndex(globalIndex);
    
    if (!userData) {
        console.error(`No user data found for index ${globalIndex}`);
        return;
    }

    // Request headers
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': authData.bearerToken
        }
    };

    // Request body based on the provided cURL
    const requestBody = JSON.stringify({
        "audit": {
            "callerId": "<EMAIL>"
        },
        "fullName": userData.fullName,
        "tenantCode": "ADINS",
        "loginId": userData.loginId,
        "roleCode": "USER_EDITOR",
        "password": "ADINS",
        "officeCode": "0191"
    });

    // Add small delay to avoid overwhelming the server
    sleep(SLEEP_DURATION);

    // Make the request
    const response = http.post(url, requestBody, requestHeader);

    // Check response
    const isSuccess = check(response, {
        'is status 200': (r) => r.status === 200,
        'response has success status': (r) => {
            try {
                const body = JSON.parse(r.body);
                return body.status && body.status.code === 0;
            } catch (e) {
                return false;
            }
        }
    });

    // Log progress every 100 requests
    if (globalIndex % 100 === 0) {
        console.log(`Progress: User ${globalIndex + 1} - LoginId: ${userData.loginId}, Status: ${response.status}`);
    }

    // Log errors for debugging
    if (!isSuccess) {
        console.error(`Failed to insert user ${globalIndex + 1} (${userData.loginId}): Status ${response.status}, Body: ${response.body}`);
    }

    // Handle rate limiting or server errors with exponential backoff
    if (response.status === 429 || response.status >= 500) {
        const backoffTime = Math.min(30, Math.pow(2, Math.floor(Math.random() * 4))); // 1-8 seconds
        console.log(`Server busy (${response.status}), backing off for ${backoffTime}s`);
        sleep(backoffTime);
    }
}

// Setup function to validate data availability
export function setup() {
    console.log("=== InsertUserManagement Load Test Setup ===");
    console.log(`Target URL: ${url}`);
    console.log(`Total users to insert: ${userManagementData.totalUsers}`);
    console.log(`Authorization: ${authData.username}`);
    console.log(`VUs: ${options.scenarios.insert_users.vus}`);
    console.log(`Iterations per VU: ${options.scenarios.insert_users.iterations}`);
    console.log(`Total requests: ${options.scenarios.insert_users.vus * options.scenarios.insert_users.iterations}`);
    
    // Validate that we have enough user data
    if (userManagementData.totalUsers < (options.scenarios.insert_users.vus * options.scenarios.insert_users.iterations)) {
        throw new Error(`Insufficient user data. Need ${options.scenarios.insert_users.vus * options.scenarios.insert_users.iterations} users, but only have ${userManagementData.totalUsers}`);
    }
    
    console.log("Setup complete. Starting load test...");
    return {};
}

// Teardown function for final reporting
export function teardown(data) {
    console.log("=== InsertUserManagement Load Test Complete ===");
    console.log("Check the K6 summary for detailed metrics.");
}
