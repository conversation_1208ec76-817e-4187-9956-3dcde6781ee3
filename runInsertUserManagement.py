#!/usr/bin/env python3
"""
Script untuk menjalankan load test InsertUserManagement
Mengatur proses generate data dan eksekusi K6 test
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n=== {description} ===")
    print(f"Command: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print("✓ Success")
        if result.stdout:
            print("Output:", result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print("✗ Failed")
        print("Error:", e.stderr)
        return False

def check_prerequisites():
    """Check if required tools are available"""
    print("=== Checking Prerequisites ===")
    
    # Check Python
    try:
        import json
        print("✓ Python with json module available")
    except ImportError:
        print("✗ Python json module not available")
        return False
    
    # Check K6
    result = subprocess.run("k6 version", shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        print(f"✓ K6 available: {result.stdout.strip()}")
    else:
        print("✗ K6 not found. Please install K6 from https://k6.io/docs/getting-started/installation/")
        return False
    
    return True

def generate_user_data():
    """Generate user data using Python script"""
    print("\n=== Generating User Data ===")
    
    if os.path.exists("master script/Data/userManagementData.js"):
        response = input("User data file already exists. Regenerate? (y/N): ")
        if response.lower() != 'y':
            print("Using existing user data file")
            return True
    
    return run_command("python generateUserData.py", "Generating 1 million user data")

def run_k6_test(script_name="master script/InsertUserManagementBatch.js"):
    """Run K6 load test"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"insert_user_management_results_{timestamp}.json"
    
    command = f'k6 run --out json={output_file} "{script_name}"'
    
    print(f"\n=== Running K6 Load Test ===")
    print(f"Script: {script_name}")
    print(f"Output: {output_file}")
    print("This may take several hours for 1 million users...")
    
    start_time = time.time()
    success = run_command(command, "K6 Load Test Execution")
    end_time = time.time()
    
    duration = end_time - start_time
    print(f"\nTest Duration: {duration/3600:.2f} hours ({duration/60:.1f} minutes)")
    
    if success and os.path.exists(output_file):
        print(f"Results saved to: {output_file}")
    
    return success

def show_menu():
    """Show interactive menu"""
    print("\n" + "="*60)
    print("    InsertUserManagement Load Test Runner")
    print("="*60)
    print("1. Generate user data only")
    print("2. Run load test (batch mode - recommended)")
    print("3. Run load test (high performance mode)")
    print("4. Generate data + Run batch test")
    print("5. Check prerequisites")
    print("0. Exit")
    print("="*60)

def main():
    """Main function"""
    print("InsertUserManagement Load Test Runner")
    print("=====================================")
    
    while True:
        show_menu()
        choice = input("\nSelect option (0-5): ").strip()
        
        if choice == "0":
            print("Goodbye!")
            break
        elif choice == "1":
            generate_user_data()
        elif choice == "2":
            if not os.path.exists("master script/Data/userManagementData.js"):
                print("User data not found. Generating first...")
                if not generate_user_data():
                    continue
            run_k6_test("master script/InsertUserManagementBatch.js")
        elif choice == "3":
            if not os.path.exists("master script/Data/userManagementData.js"):
                print("User data not found. Generating first...")
                if not generate_user_data():
                    continue
            run_k6_test("master script/InsertUserManagement.js")
        elif choice == "4":
            if generate_user_data():
                run_k6_test("master script/InsertUserManagementBatch.js")
        elif choice == "5":
            check_prerequisites()
        else:
            print("Invalid option. Please try again.")
        
        input("\nPress Enter to continue...")

if __name__ == "__main__":
    # Check prerequisites first
    if not check_prerequisites():
        print("\nPlease install missing prerequisites and try again.")
        sys.exit(1)
    
    main()
