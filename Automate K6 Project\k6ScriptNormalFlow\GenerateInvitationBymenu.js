import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import users from "../data/user.js";
import baseurl from "../data/baseurl.js";
import auth from "../data/auth.js"


let baseurls = baseurl[0];
let authData = auth[4];

const url = baseurls.url +"/services/user/s/generateInvitationLinkSecured";

const SLEEP_DURATION = 1;

export let options = {
  discardResponseBodies: false,
  scenarios: {

   contacts: {
      executor: 'per-vu-iterations',
      vus: 20,
      iterations: 1,
      maxDuration: '10m',
    },
  },
};

// Test scenario
export default function () {

  const requestHeader = {
    headers: {
      'Content-Type': 'application/json'
      ,'Authorization': authData.bearerToken

    }
  };

  let user = users[exec.vu.idInTest - 1];
 
  if (user == null) {
    user = {
      "username": ""
      , "nik": ""
      , "nohp": ""
    };
  }
  // Get User Profile (user/s/profiles)
  const requestBody = JSON.stringify({

    audit: { callerId: user.username }
    , tenantCode: "ADINS"
    , users: [{
      nama: user.username,
      tlp: user.nohp,
      email: [user.username]+ "@esignhub.my.id",
      jenisKelamin: "M",
      tmpLahir: "BOGOR",
      tglLahir: user.date,
      idKtp: user.nik,
      provinsi: "Jawa Barat",
      kota: "Bogor",
      kecamatan: "Bogor Selatan",
      kelurahan: "Baranangsiang",
      kodePos: "16143",
      alamat: "JL. SAWOKNA NO.1000 BANTAR KEMANG"
      , vendorCode: "VIDA"
    }]


  });

  const response = http.post(url, requestBody, requestHeader);
  console.log(response.body);
  sleep(SLEEP_DURATION);

  check(response, {
    'is status 200': (r) => r.status === 200 
  });
}
