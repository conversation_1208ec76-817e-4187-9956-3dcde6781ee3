import http from "k6/http";
import { check, sleep } from "k6";
import baseurl from "./master script/Data/baseurl.js";
import authorization from "./master script/Data/authorization-library.js";

// Configuration
let baseurls = baseurl[0];
const url = baseurls.url + "/services/user-management/s/insertUserManagement";
const authData = authorization[3]; // <EMAIL>

// Test different office codes
const officeCodesToTest = [
    "0071", // Original from cURL
    "0001", // Common office codes
    "0000",
    "001",
    "01",
    "1",
    "ADINS",
    "HEAD",
    "MAIN",
    "PUSAT"
];

export default function () {
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': authData.bearerToken
        }
    };

    console.log("Testing different office codes with USER_EDITOR role...");

    officeCodesToTest.forEach((officeCode, index) => {
        const testLoginId = `OFFICETEST.${officeCode}${index}@TEST.COM`;
        
        const requestBody = JSON.stringify({
            "audit": {
                "callerId": "<EMAIL>"
            },
            "fullName": `Test User Office ${officeCode}`,
            "tenantCode": "ADINS",
            "loginId": testLoginId,
            "roleCode": "USER_EDITOR",
            "password": "ADINS",
            "officeCode": officeCode
        });

        console.log(`\n--- Testing officeCode: ${officeCode} ---`);
        console.log(`LoginId: ${testLoginId}`);

        const response = http.post(url, requestBody, requestHeader);
        
        console.log(`Status: ${response.status}`);
        console.log(`Response: ${response.body}`);

        try {
            const responseBody = JSON.parse(response.body);
            if (responseBody.status) {
                if (responseBody.status.code === 0) {
                    console.log(`✓ SUCCESS: officeCode "${officeCode}" is valid!`);
                    console.log(`✓ Use: roleCode=USER_EDITOR, officeCode=${officeCode}`);
                } else {
                    console.log(`✗ FAILED: ${responseBody.status.message} (Code: ${responseBody.status.code})`);
                }
            }
        } catch (e) {
            console.log(`✗ FAILED: Could not parse response`);
        }

        sleep(1); // Wait 1 second between tests
    });
}

export let options = {
    vus: 1,
    iterations: 1,
};
