import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';

import baseurl from "./Data/baseurl.js";
import tenant from "./Data/tenant-library.js";


let baseurls = baseurl[0];
const url = baseurls.url + "/services/embed/user/sentOtpSigningEmbed";

const SLEEP_DURATION = 5;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {
    let tenantKey = tenant[1];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'

        }
    };

  
    const requestBody = JSON.stringify({
        audit:{
            callerId:"<EMAIL>"
        },
        vendorCode:"VIDA",
        documentId: ["00155D0A-AD03-9632-11EE-2C5456B23A60"],
        phoneNo : "081297723819",
        msg : "zRGQaG59B8dHnobnG0PoHsJHcHXJwITv/Qjw4D5ypM6pWtHWzl2/feN8WO9NlTIfTcm0GKYeE3/CkzuYnn8SMT/dk9QBdDQJAoslPcoqYOKmEKpOLq/4KXIe4gDsheJ3fsHqSQX7+dVhiAfYnO7WpQ==",
        sendingPointOption : "SMS",
        tenantCode : "ADINS"

    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(response.body);




}
