import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import APIS from "../apis.js";

const BASE_URL = "http://gdkwebsvr:7021/adimobile/esign/services";
const SLEEP_DURATION = 1;
const USERNAME = "<EMAIL>";
const PASSWORD = "supersupeR4!";
const USERNAME_2 = "<EMAIL>";


// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '10m',
        },
    },
};

// Test scenario
export default function () {

    const paramsRegistration = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': 'ASDFGH@WOMF'
            //  ,'Cookie' : 'idjs=FDD47A1F028E0E6900095FC1D1395777'
        }
    };

    // Random e-mail suffix

    let apis = APIS[0][exec.vu.idInTest - 1];

    if (apis == null) {
        apis = {
            "username": ""
            , "nik": ""
            , "nohp": ""
        };
    }


    const registerrequest = JSON.stringify({

        audit: { callerId: apis.username },
        msg: "cif5r/6md/1lB1I8LEso9H+2y2n3IoSo3CdFLnJW1Cn8d6fNP8qtRpfVpUN2XNc+4xR5cVe+LGbXuUp2rv/Npg==",
        tenantCode: "WOMF",
        ipAddress: "*************",
        browser: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        documentIds: [
            "00155D0B-7502-8D5B-11ED-CB16D62D1070",
            "00155D0B-7502-8D5B-11ED-CB16D62D1070"
        ]
    }, null, "\t");

    const url = BASE_URL + "/embed/document/signConfirmDocument";
    const registerResponse = http.post(url, registerrequest, paramsRegistration);
    check(registerResponse, {
        'is status 200': (r) => r.status === 200,

    });

    sleep(SLEEP_DURATION);


}
