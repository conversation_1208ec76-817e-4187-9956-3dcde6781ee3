import { check } from 'k6';
import env, {vus} from '../environment.js';
import http from 'k6/http';
import {getCurrentTimestampString} from '../basic-function.js';

const documentBase64 = open('./Data/document-base64.txt');

export let options = env.loadTestOptions;

export default function() {

    const currentIteration = __ITER;
    const currentVus = __VU;
    const index = (currentIteration * vus) + (currentVus - 1);

    const url = env.baseUrl + '/services/external/document/sendDocumentSigning';
    const refNumber = 'LT_' + getCurrentTimestampString() + '_' + index;

    const payload = JSON.stringify({
        audit: {
            callerId: 'LOADTEST'
        },
        tenantCode: 'ADINS',
        psreCode: 'VIDA',
        requests: [
            {
                referenceNo: refNumber,
                documentTemplateCode: 'LT_1CUST_1BM',
                documentName: 'Load Test Document',
                officeCode: 'LT_OFFICE',
                officeName: 'Load Test Office',
                signers: [
                    {
                        signAction: 'mt',
                        signerType: 'CUST',
                        tlp: '08111128600',
                        idKtp: '3511000101806305',
                        email: '<EMAIL>'
                    },
                    {
                        signAction: 'at',
                        signerType: 'MF',
                        tlp: '081363853152',
                        idKtp: '3271011312910014',
                        email: '<EMAIL>'
                    }
                ],
                stampLocations: [],
                documentFile: documentBase64
            }
        ]
    });

    const param = {
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'x-api-key': 'Gy6Ka5XhgRmq@ADINS'
        }
    };

    const response = http.post(url, payload, param);
    const responseBody = response.json();
    
    check(response, {
        'HTTP code 200' : (resp) => resp.status === 200,
    });
    check(responseBody, {
        'Status code 0' : (body) => body.status.code === 0
    });
    
    console.log(response.body);
}