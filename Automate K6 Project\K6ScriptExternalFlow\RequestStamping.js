import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import baseurl from "../data/baseurl.js";
import stampDocuments from "../data/stampDocumentId.js";
import tenants from "../data/apiKey.js";


let baseurls = baseurl[0];
const url = baseurls.url + "/services/external/document/requestStamping";
let tenant = tenants[1];
const SLEEP_DURATION = 2;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': tenant.xapikey

        }
    };

    let stampDocument = stampDocuments[exec.vu.idInTest - 1];
  
    const requestBody = JSON.stringify({
        
        audit:{
            callerId: "ADMINADINS"
        },
        refNumber: stampDocument.refNumber

    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });
    console.log(response.body);
}