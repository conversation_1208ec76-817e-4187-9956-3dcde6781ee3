import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./Data/authorization-library.js";
import unsignDocument from "./Data/unsignDocumentId.js";
import baseurl from "./Data/baseurl.js"

let baseurls = baseurl[0]
const URL = baseurls.url  + "/services/document/s/checkDocumentBeforeSigning";

const SLEEP_DURATION = 2;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {

    let auths = auth[0];
    let listDoc = unsignDocument[(exec.vu.idInInstance - 1) % unsignDocument.length];

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'Authorization': auths['bearerToken']
        }
    };

    const requestBody = JSON.stringify({

        loginId: auths.username,
        listDocumentId: [
            listDoc.docId
        ],
        audit: {
            callerId: "default"
        },
        tenantCode: "ADINS"

    });
    
    const response = http.post(URL, requestBody, requestHeader);

    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200
    });

     console.log([exec.vu.idInTest - 1], response.body);

}
