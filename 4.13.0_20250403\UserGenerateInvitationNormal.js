import { check } from 'k6';
import env, {vus} from '../environment.js';
import http from 'k6/http';
import authorization from '../Automate K6 Project/data/auth.js';

export let options = env.loadTestOptions;
export let username = authorization[4].username;
export let password = authorization[4].password;

export default function() {

    const currentIteration = __ITER;
    const currentVus = __VU;
    const index = (currentIteration * vus) + (currentVus - 1);
    console.log(index);

    const url = env.baseUrl + '/services/user/generateInvitationLink';

    const payload = JSON.stringify({
        audit: {
            callerId: username
        },
        tenantCode: 'ADINS',
        psreCode: 'VIDA',
        users: [{
            email: "",
            tlp: index.toString().padStart(12, '0'),
            jenisKelamin: "M",
            tmpLahir: "Bogor",
            tglLahir: "1980-01-01",
            idKtp: index.toString().padStart(16, '0'),
            provinsi: "<PERSON>awa Barat",
            kota: "<PERSON><PERSON>",
            kecamatan: "<PERSON><PERSON>",
            kel<PERSON><PERSON>: "<PERSON><PERSON><PERSON>",
            kodePos: "16142",
            alamat: "Jl. <PERSON>a <PERSON>jajaran, RT.04/RW.01",
            selfPhoto: "",
            idPhoto: "",
            nama: "Load Test"
        }]

    });

    const param = {
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'x-api-key': 'Gy6Ka5XhgRmq@ADINS'
        }
    };

    const response = http.post(url, payload, param);
    const responseBody = response.json();

    check(response, {
        'HTTP code 200' : (resp) => resp.status === 200,
    });
    check(responseBody, {
        'Status code 0' : (body) => body.status.code === 0
    });

    console.log(response.body);
}