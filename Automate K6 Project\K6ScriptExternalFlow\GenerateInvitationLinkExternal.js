import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import tenants from "../data/apiKey.js";
import baseurl from "../data/baseurl.js";
import users from "../data/user.js";

let baseurls = baseurl[0];
const url = baseurls.url + "/services/external/user/generateInvLink";
const SLEEP_DURATION = 1;
let tenant = tenants[1];

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': tenant.xapikey
        }
    };

    let user = users[exec.vu.idInTest - 1];
 
    if (user == null) {
      user = {
        "username": ""
        , "nik": ""
        , "nohp": ""
      };
    }

    const requestBody = JSON.stringify({

        audit: {
            callerId: "default"
        },
        email: [user.username]+ "@esignhub.my.id",
        idKtp: user.nik,
        jenisKelamin: "M",
        kecamatan: "Tanjung Priok",
        kelurahan: "Sunter Agung",
        kodePos: "14350",
        kota: "JAKARTA UTARA",
        nama: user.username,
        provinsi: "DKI JAKARTA",
        tglLahir: user.date,
        tlp: user.nohp,
        tmpLahir: "TANGERANG",
        type: "EMPLOYEE"
    });
    
    const response = http.post(url, requestBody, requestHeader);

    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200
    });

    console.log([exec.vu.idInTest - 1], response.body);

}
