#!/usr/bin/env python3
"""
Script untuk generate 1 juta data user unik untuk load testing InsertUserManagement
Menghasilkan kombinasi loginId dan fullName yang unik
"""

import json
import random
import string
from datetime import datetime

def generate_unique_login_id(index):
    """Generate unique loginId dengan format USERMANAGEMENT.{PREFIX}{INDEX}@TEST.COM"""
    # Menggunakan berbagai prefix untuk variasi
    prefixes = ['ANDY', 'BETA', 'CHARLIE', 'DELTA', 'ECHO', 'FOXTROT', 'GOLF', 'HOTEL', 'INDIA', 'JULIET']
    prefix = prefixes[index % len(prefixes)]
    return f"USERMANAGEMENT.{prefix}{index}@TEST.COM"

def generate_unique_full_name(index):
    """Generate unique fullName dengan kombinasi nama depan dan belakang"""
    first_names = [
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
    ]
    
    last_names = [
        '<PERSON>', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez',
        'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin',
        'Lee', 'Perez', 'Thompson', 'White', 'Harris', 'Sanchez', 'Clark', 'Ramirez', 'Lewis', 'Robinson',
        'Walker', 'Young', 'Allen', 'King', 'Wright', 'Scott', 'Torres', 'Nguyen', 'Hill', 'Flores',
        'Green', 'Adams', 'Nelson', 'Baker', 'Hall', 'Rivera', 'Campbell', 'Mitchell', 'Carter', 'Roberts'
    ]
    
    # Kombinasi nama dengan index untuk memastikan keunikan
    first_name = first_names[index % len(first_names)]
    last_name = last_names[(index // len(first_names)) % len(last_names)]
    
    # Tambahkan suffix numerik jika diperlukan untuk keunikan
    if index >= (len(first_names) * len(last_names)):
        suffix = index // (len(first_names) * len(last_names))
        return f"{first_name} {last_name} {suffix}"
    else:
        return f"{first_name} {last_name}"

def generate_user_data(total_users=1000000):
    """Generate data untuk sejumlah user"""
    users = []
    
    print(f"Generating {total_users:,} user data...")
    
    for i in range(total_users):
        if i % 10000 == 0:
            print(f"Progress: {i:,}/{total_users:,} ({(i/total_users)*100:.1f}%)")
        
        user = {
            "loginId": generate_unique_login_id(i),
            "fullName": generate_unique_full_name(i),
            "index": i
        }
        users.append(user)
    
    return users

def save_to_js_file(users, filename="master script/Data/userManagementData.js"):
    """Save user data ke file JavaScript untuk K6"""
    print(f"Saving {len(users):,} users to {filename}...")
    
    # Split data menjadi chunks untuk menghindari file terlalu besar
    chunk_size = 50000  # 50k users per chunk
    chunks = [users[i:i + chunk_size] for i in range(0, len(users), chunk_size)]
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write("// Auto-generated user data for InsertUserManagement load testing\n")
        f.write(f"// Generated on: {datetime.now().isoformat()}\n")
        f.write(f"// Total users: {len(users):,}\n\n")
        
        f.write("const userManagementData = {\n")
        f.write(f"    totalUsers: {len(users)},\n")
        f.write(f"    chunkSize: {chunk_size},\n")
        f.write("    chunks: [\n")
        
        for chunk_index, chunk in enumerate(chunks):
            f.write(f"        // Chunk {chunk_index + 1}: users {chunk_index * chunk_size} - {min((chunk_index + 1) * chunk_size - 1, len(users) - 1)}\n")
            f.write("        [\n")
            
            for user in chunk:
                f.write(f'            {{"loginId": "{user["loginId"]}", "fullName": "{user["fullName"]}", "index": {user["index"]}}},\n')
            
            f.write("        ],\n")
        
        f.write("    ],\n")
        f.write("    \n")
        f.write("    // Helper function to get user by global index\n")
        f.write("    getUserByIndex: function(index) {\n")
        f.write("        const chunkIndex = Math.floor(index / this.chunkSize);\n")
        f.write("        const userIndex = index % this.chunkSize;\n")
        f.write("        if (chunkIndex < this.chunks.length && userIndex < this.chunks[chunkIndex].length) {\n")
        f.write("            return this.chunks[chunkIndex][userIndex];\n")
        f.write("        }\n")
        f.write("        return null;\n")
        f.write("    }\n")
        f.write("};\n\n")
        f.write("module.exports = userManagementData;\n")
    
    print(f"Successfully saved {len(users):,} users to {filename}")
    print(f"File size: {len(chunks)} chunks of {chunk_size:,} users each")

def main():
    """Main function"""
    print("=== User Management Data Generator ===")
    print("Generating 1 million unique users for InsertUserManagement load testing")
    
    # Generate user data
    users = generate_user_data(1000000)
    
    # Save to JavaScript file
    save_to_js_file(users)
    
    print("\n=== Generation Complete ===")
    print(f"Total users generated: {len(users):,}")
    print("File: master script/Data/userManagementData.js")
    print("\nSample data:")
    for i in range(min(5, len(users))):
        user = users[i]
        print(f"  {i+1}. LoginId: {user['loginId']}, FullName: {user['fullName']}")

if __name__ == "__main__":
    main()
