import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import APIS from "./Data/apis.js";
import tenant from "./Data/tenant-library.js";
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const url = baseurls.url + "/oauth/token";
const SLEEP_DURATION = 2;

export let options = {
  discardResponseBodies: false,
  scenarios: {
    contacts: {
      executor: 'per-vu-iterations',
      vus: 20,
      iterations: 1,  // Set to the total number of iterations
      maxDuration: '10m',
    },
  },
};

// Define credentials
const credentials = [
  { loginId: "<EMAIL>", password: "P@ssw0rd123" },
  { loginId: "<EMAIL>", password: "Password123!" },
  { loginId: "<EMAIL>", password: "Password123!" },
  { loginId: "<EMAIL>", password: "Password123!" },
  { loginId: "<EMAIL>", password: "Password123!" },
  { loginId: "<EMAIL>", password: "Password123!" },
  { loginId: "<EMAIL>", password: "Password123!" },
  { loginId: "<EMAIL>", password: "Password123!" },
  { loginId: "<EMAIL>", password: "Password123!" },
  { loginId: "<EMAIL>", password: "P@ssw0rd123" },
  { loginId: "<EMAIL>", password: "Password123!" },
  { loginId: "<EMAIL>", password: "Password123!" },
  { loginId: "<EMAIL>", password: "Password123!" },
  { loginId: "<EMAIL>", password: "P@ssw0rd123" },
  { loginId: "<EMAIL>", password: "Password123!" },
  { loginId: "<EMAIL>", password: "Password123!" },
  { loginId: "<EMAIL>", password: "Password123!" },
  { loginId: "<EMAIL>", password: "Password123!" },
  { loginId: "<EMAIL>", password: "P@ssw0rd123" }
];

// Test scenario
export default function () {
  let tenantKey = tenant[0][1];
  const requestHeader = {
    headers: {
      'Content-Type': 'application/json',
    }
  };

    const formData = {
      client_id: "frontend",
      grant_type: "password",
      username: credentials[exec.vu.idInInstance].loginId,
      password: credentials[exec.vu.idInInstance].password
    };

  const response = http.post(url, formData);
  sleep(SLEEP_DURATION);

  check(response, {
    'is status 200': (r) => r.status === 200
  });

  console.log(response.body);
  // console.log(credentials[exec.vu.idInInstance].loginId)
  // console.log(credentials[exec.vu.idInInstance].password)
  // console.log(exec.vu.idInInstance)

}
