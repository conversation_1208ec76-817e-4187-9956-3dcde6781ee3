import http from 'k6/http';

export function login(baseUrl, username, password) {
    const loginUrl = baseUrl + '/oauth/token';

    const payload = {
        client_id: 'frontend',
        grant_type: 'password',
        username: username,
        password: password
    };

    const param = {
        headers: {'Content-Type': 'application/x-www-form-urlencoded'}
    };

    const response = http.post(loginUrl, payload, param);
    console.log(`Login response: ${response.body}`);

    const jsonObject = response.json();
    if (!jsonObject.access_token) {
        throw new Error('Token not found in response');
    }

    return jsonObject.access_token;
}

/**
 * Return string in yyyyMMddHHMMss format
 */
export function getCurrentTimestampString() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0'); // Months are zero-based
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    return `${year}${month}${day}${hours}${minutes}${seconds}`;
}

export function generateGUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}