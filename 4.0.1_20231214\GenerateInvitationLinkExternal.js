import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import tenant from "./Data/tenant-library.js";
import baseurl from "./Data/baseurl.js";
import APIS from "./Data/apis.js";

let baseurls = baseurl[0];
const url = baseurls + "/external/user/generateInvLink";
const SLEEP_DURATION = 1;
let apiKey = tenant[0][1];


// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': apiKey.xapikey
        }
    };

    let apis = APIS[0][exec.vu.idInTest];
 
    if (apis == null) {
        apis = {
        "username": ""
        , "nik": ""
        , "nohp": ""
        };
    }
    // G

    const requestBody = JSON.stringify({

        audit: {
            callerId: "default"
        },
        msg : "",
        nama : apis.username,
        idKtp: apis.nik,
        email: "MALVINTEST23120101" + [exec.vu.idInTest ] + "@esignhub.my.id",
        tlp : apis.nohp  + [exec.vu.idInTest],
        jenisKelamin: "M",
        tmpLahir: "BOGOR",
        tglLahir: "1980-01-01",
        alamat: "JL. SAWOKNA NO.1000 BANTAR KEMANG",
        kelurahan: "Baranangsiang",
        kecamatan: "Bogor Selatan",
        kota: "Bogor",
        provinsi: "Jawa Barat",
        kodePos: "16143",
        selfPhoto : "",
        idPhoto : "",
        region : "",
        office : "",
        businessLine : "",
        taskNo : "",
        type :""


    });
    
    const response = http.post(url, requestBody, requestHeader);

    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200
    });

    console.log([exec.vu.idInTest - 1], response.body);

}
