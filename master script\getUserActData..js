import http from "k6/http";
import { check, sleep } from "k6";
import tenant from "./Data/tenant-library.js";
import baseurl from "./Data/baseurl.js";


let baseurls = baseurl[0];
const url = baseurls.url  + "/services/user/getUserActData";
const SLEEP_DURATION = 2;

export let options = {
    discardResponseBodies: false,
    scenarios: {
        getUserActData: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 2,
            maxDuration: '10m',
        },
    },
};

export default function () {
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json',
            'Cookie': 'idjs=4C6C9ED400506A9790237FFEE9BA79CF'
        }
    };

    const requestBody = JSON.stringify({
        audit: { callerId: "" },
        msg: "2qOPVJAlLlqq68xlC3/xag==",
    });

    const response = http.post(url, requestBody, requestHeader);

    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200
    });

    console.log(response.body);
}
