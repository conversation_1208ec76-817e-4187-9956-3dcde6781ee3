import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import APIS from "./Data/apis.js";
import baseurl from "./Data/baseurl.js";
import tenant from "./Data/tenant-library.js";


let baseurls = baseurl[0];
const url = baseurls.url +"/services/user/sendOtpEmailInvitation";

const SLEEP_DURATION = 5;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {
    let tenantKey = tenant[0][1];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
        }
    };

    let apis = APIS[0][exec.vu.idInTest];
 
    if (apis == null) {
        apis = {
        "username": ""
        , "nik": ""
        , "nohp": ""
        };
    }




    const requestBody = JSON.stringify({

        audit: { callerId:"MALVIN" },
        loginId: "<EMAIL>",
        msg : "N5J1ZBhFCPa7MClC8bP3aQ==",
    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    // console.log(url);
    // console.log(tenantKey['xapikey']);
    console.log(response.body);




}
