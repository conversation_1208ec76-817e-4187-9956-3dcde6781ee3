import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
// import baseurl from "./Data/baseurl.js";

// let baseurls = baseurl[0]
// const URL = baseurls.url + "/services/external";
const SLEEP_DURATION = 2;
const USERNAME = "<EMAIL>";
const PASSWORD = "supersupeR4!";
const USERNAME_2 = "<EMAIL>";

// setup per iteration
export let options = {
    discardResponseBodies: false,
    // tls_insecure_skip_verify: true,
    insecureSkipTLSVerify: true,
    scenarios: {
        contacts: {
            executor: 'per-vu-iterations',
            vus: 200,
            iterations: 122323,
            maxDuration: '10m',
        },
    },
};

// Test scenario
export default function () {
    const paramscheckregistration = {
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'x-api-key': '<PERSON><PERSON><PERSON>pt<PERSON>@TAFS'
        },
        tls_insecure_skip_verify: true // Bypass SSL certificate validation
    };

    const generateCheckRegistationRequest = JSON.stringify({
        audit: { callerId: "UserIBAA" },
        dataType: "NIK",
        userData: "3271011312910014"
    });

    const url = "https://esignhub.ad-ins.com/adimobile/esign/services/external/user/checkRegistration";
    const checkRegistration = http.post(url, generateCheckRegistationRequest, paramscheckregistration);

    sleep(SLEEP_DURATION);

    check(checkRegistration, {
        'is status 200': (r) => r.status === 200
    });

   // console.log(checkRegistration.body);
}
