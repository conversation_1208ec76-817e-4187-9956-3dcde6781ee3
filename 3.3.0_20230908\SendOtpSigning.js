import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import APIS from "../apis.js";

const BASE_URL = "https://esignhub.docsol.id:543/adimobile/esign/services/external";
const SLEEP_DURATION = 5;
const USERNAME = "<EMAIL>";
const PASSWORD = "supersupeR4!";
const USERNAME_2 = "<EMAIL>";


// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 100,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {

    const paramSendOtpSigning = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': 'ASDFGH@WOMF'
            //  ,'Cookie' : 'idjs=FDD47A1F028E0E6900095FC1D1395777'
        }
    };

    // // Random e-mail suffix
    // let document = DOC[0][0];
    let apis = APIS[0][exec.vu.idInTest - 1];

    if (apis == null) {
        apis = {
            "username": ""
            , "nik": ""
            , "nohp": ""
        };
    }


    const sendotprequest = JSON.stringify({

        audit: { callerId:"MALVIN" },
        phoneNo: "000101809001",
        email: "KELVINLOADTEST001" + [exec.vu.idInTest] + "@esignhub.my.id",
        refNumber: ""
    }, null, "\t");

    const url = BASE_URL + "/user/sentOtpSigning";
    const sendOtpResponse = http.post(url, sendotprequest, paramSendOtpSigning);
    check(sendOtpResponse, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(sendOtpResponse.body);

    sleep(SLEEP_DURATION);


}
