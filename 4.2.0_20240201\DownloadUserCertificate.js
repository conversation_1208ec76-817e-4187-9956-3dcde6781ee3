import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import datauser from './Data/apis.js';
import tenantLibrary from './Data/tenant-library.js';
import baseurl from "./Data/baseurl.js";
import dataUser from './Data/authorization-library.js';

let baseurls = baseurl[0];
const url = baseurls.url  + "/services/user/checkInvitationRegisterStatus";
const SLEEP_DURATION = 5;
let apiKey = tenantLibrary[0][1];



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 1,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': apiKey.xapikey
            //  ,'Cookie' : 'idjs=FDD47A1F028E0E6900095FC1D1395777'
        }
    };

    let dataUsers = dataUser[0][exec.vu.idInTest]

    const requestBody = JSON.stringify({

        audit: { callerId:dataUsers.username },
        email: "<EMAIL>",
        phone: "085668305583",
        psreCode: "TKNAJ"
    }, null, "\t");

 
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(response.body);

    sleep(SLEEP_DURATION);


}
