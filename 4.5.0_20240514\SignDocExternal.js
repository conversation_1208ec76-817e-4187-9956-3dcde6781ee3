import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import baseurl from "./Data/baseurl.js";
import tenant from "./Data/tenant-library.js";


let baseurls = baseurl[0];
const URL = baseurls.url  +  "/services/external/document/signDocument";

const SLEEP_DURATION = 5;


export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};


const listDOc = ['00163E04-1D85-A9D7-11EF-1298E5762970',
'00163E04-1D85-A9D7-11EF-1298E56D4FD0',
'00163E04-1D85-A9D7-11EF-1298E5640100',
'00163E04-1D85-A9D7-11EF-1298E55A8B20',
'00163E04-1D85-A9D7-11EF-1298E550C720',
'00163E04-1D85-A9D7-11EF-1298E5472A30',
'00163E04-1D85-A9D7-11EF-1298E53E5090',
'00163E04-1D85-A9D7-11EF-1298E534B3A0',
'00163E04-1D85-A9D7-11EF-1298E52A7A70',
'00163E04-1D85-A9D7-11EF-1298E51FF320',
'00163E04-1D85-A9D7-11EF-1298E514CF90',
'00163E04-1D85-A9D7-11EF-1298E50B80C0',
'00163E04-1D85-A9D7-11EF-1298E501BCC0',
'00163E04-1D85-A9D7-11EF-1298E4F78390',
'00163E04-1D85-A9D7-11EF-1298E4EE0DB0',
'00163E04-1D85-A9D7-11EF-1298E4D9E970',
'00163E03-FD4A-A4CE-11EF-1298E4C13150',
'00163E03-FD4A-A4CE-11EF-1298E4B59890',
'00163E03-FD4A-A4CE-11EF-1298E4A9B1B0',
'00163E03-FD4A-A4CE-11EF-1298E49DCAD0',]


// Test scenario
export default function () {
    let tenantKey = tenant[1];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': 'Gy6Ka5XhgRmq@ADINS'


        }
    };

    const requestBody = JSON.stringify({

        
        audit: {
            callerId: "default"
        },
        documentId: [listDOc[exec.vu.idInInstance]]
        ,email : "<EMAIL>"
        ,password : "Password123!"
        ,ipAddress : "***********"
        , browserInfo : "Browser Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
        ,otp : "999999"
        ,selfPhoto : ""

    });

    sleep(SLEEP_DURATION);
    const response = http.post(URL, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });
    console.log(response.body);

}
