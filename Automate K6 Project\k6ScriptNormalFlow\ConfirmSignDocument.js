import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import users from "../data/user.js";
import baseurl from "../data/baseurl.js";

let baseurls = baseurl[0];
const url = baseurls.url  + "/services/document/s/signConfirmDokumen";
const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {

    let user = users[exec.vu.idInTest - 1];
    
    if (user == null) {
        user = {
        "username": ""
        , "nik": ""
        , "nohp": ""
        };
    }

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'Authorization': user.bearerToken
        }
    };

    const requestBody = JSON.stringify({

        audit: {
            callerId: "default"
        },
        email: user.username + "@esignhub.my.id",
        ipAddress: "************",
        browser: "mozila",
        documentId: [
            user.documentId
        ]

    });
    
    const response = http.post(url, requestBody, requestHeader);

    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200
    });

    console.log([exec.vu.idInTest - 1 - 1], response.body);

}
