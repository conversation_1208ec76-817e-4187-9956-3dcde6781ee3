import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import baseurl from "../data/baseurl.js";
import users from "../data/user.js";


let baseurls = baseurl[0];
const url = baseurls.url + "/services/document/resendSignNotif";

const SLEEP_DURATION = 5;

let authData = auth[1];

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {
    let tenantKey = tenant[0][1];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
        }
    };

    let user = users[exec.vu.idInTest - 1];
 
    if (user == null) {
        user = {
        "username": ""
        , "nik": ""
        , "nohp": ""
        };
    }

    const requestBody = JSON.stringify({
        msg: "+EH8s0REprgUGpj94nKz5cfrobCK/QmA6MEcXUWTzyTkMC5k0Z/vC/aFnrDRLNoewu7/HSmGQSmGM16alPlD1zE1s/xM8Qkx3lgBe37ji2CI41E7O2vt5/bQ3z/+iMwrDM3oJeAAkf1MswkxG4Xf+g==",
        documentId : user.documentId,
        tenantCode: "ADINS",
        audit: {
            callerId: user.username + "esignhub.my.id"
        }
    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(response.body);




}
