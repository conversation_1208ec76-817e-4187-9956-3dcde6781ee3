import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import tenants from "../data/apiKey.js";
import baseurl from "../data/baseurl.js";
import auth from "../data/auth.js"

let baseurls = baseurl[0];
const url = baseurls.url  + "/services/data/s/getGeneralSetting";
const SLEEP_DURATION = 5;
let authData = auth[6];



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            ,'Authorization': authData.bearerToken
        }
    };

    const requestBody = JSON.stringify({

        audit: { callerId:"ADMESIGN" },
        gsCode : "EXPIRED_BALANCE_DURATION"

        
    }, null, "\t");

 
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(response.body);

    sleep(SLEEP_DURATION);


}
