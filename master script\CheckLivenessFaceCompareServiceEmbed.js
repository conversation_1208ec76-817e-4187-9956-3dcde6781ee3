import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';

import auth from "./Data/authorization-library.js";
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const url = baseurls.url + "/services/embed/tenant/checkLivenessFaceCompareServiceEmbed";
const SLEEP_DURATION = 2;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 2,
            iterations: 1,
            maxDuration: '10m',
        },
    },
};

// Test scenario
export default function () {
    let auths = auth[0][0];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            
            //  ,'Cookie' : 'idjs=FDD47A1F028E0E6900095FC1D1395777'
        }
    };


    // Get User Profile (user/s/profiles)
    const requestBody = JSON.stringify({

        audit: { callerId: "ADMESIGN" },
        msg: "jJwIXY4SY5WmgDDCFMaeezsXZMLFVqawcPY2Iu6jc4mBgulnv9d7pKp9bj+jrERIJoZV1utTZQ72KhUwVpJgVTzPr11IwGFFy8odM65eJU8Xfva7Tb23AULBKaXlNF4b",
        tenantCode: "ADINS"

    });
    const response = http.post(url, requestBody, requestHeader);
  
    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200

    });

    console.log(response.body);

}
