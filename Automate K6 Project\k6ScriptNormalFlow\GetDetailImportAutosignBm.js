import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import users from "../data/user.js";
import baseurl from "../data/baseurl.js";
import auths from "../data/auth.js";

let baseurls = baseurl[0];
const url = baseurls.url +"/services/autosign/s/detailImportBmAutosign";
let auth = auths[3];

const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            ,'Authorization': auth.bearerToken
        }
    };

    let user = users[exec.vu.idInTest - 1];
 
    if (users == null) {
        users = {
        "username": ""
        , "nik": ""
        , "nohp": ""
        };
    }

    
    const requestBody = JSON.stringify({

        audit: { 
            callerId: auth.username 
        },
        fileName: "templateImportBm.xlsx",
        requestDate : "2025-01-08 10:47:51.641",
        tenantCode : "ADINS",
        page: 1   
    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });
    console.log(response.body);




}
http-nio-7023-exec-851
http-nio-7023-exec-958
http-nio-7023-exec-943
http-nio-7023-exec-957
http-nio-7023-exec-941
http-nio-7023-exec-956
http-nio-7023-exec-961
http-nio-7023-exec-843
http-nio-7023-exec-935
http-nio-7023-exec-959
http-nio-7023-exec-949
http-nio-7023-exec-954
http-nio-7023-exec-946
http-nio-7023-exec-952
http-nio-7023-exec-962
http-nio-7023-exec-960
http-nio-7023-exec-953
http-nio-7023-exec-1
http-nio-7023-exec-2
http-nio-7023-exec-7