/*
    Setiap mau run yang membutuhkan token, run ini API dahulu

    Url: {http://gdkwebsvr:7021/adimobile/esign/oauth/token}
*/

const authorization = 
    [
        {
            // 0
            username      : '<EMAIL>',
            password      : 'Password123!',
            bearerToken   : 'Bearer ' + 'madv5vAAhfSnVXEdum95tvNFu+k=',
            msg           : 'Pvkay+O8L4Wg/1CNGMLhW/S+yC6Ea0VnV5HuBhiqZ58OLQ34hshCPd3NbGINegR45hOdKC3Q8vWlMS0VGQnS1Q=='
        },
        {
            // 0
            username      : '<EMAIL>',
            password      : 'password',
            bearerToken   : 'Bearer ' + '0iT8xe3+p1V7+SP21aXu1n8NeRM=',
            msg           : 'Pvkay+O8L4Wg/1CNGMLhW/S+yC6Ea0VnV5HuBhiqZ58OLQ34hshCPd3NbGINegR45hOdKC3Q8vWlMS0VGQnS1Q=='
        },
        {
            // 1
            username      : 'HELPDESK@ADINS',
            password      : 'password',
            bearerToken   : 'Bearer ' + 'LtWc1KVifr+aSVC8kyFJCWLd6d4=',
            msg           : 'Pvkay+O8L4Wg/1CNGMLhW/S+yC6Ea0VnV5HuBhiqZ58OLQ34hshCPd3NbGINegR45hOdKC3Q8vWlMS0VGQnS1Q=='
        },
        {
            // 2
            username      : '<EMAIL>',
            password      : 'password',
            bearerToken   : 'Bearer ' + 'KJA1yro6TpDjTR57Bnl13+q+urw=',
            msg           : 'Pvkay+O8L4Wg/1CNGMLhW/S+yC6Ea0VnV5HuBhiqZ58OLQ34hshCPd3NbGINegR45hOdKC3Q8vWlMS0VGQnS1Q=='

        },
        {
            // 3
            username      : '<EMAIL>',
            password      : 'Password123!',
            bearerToken   : 'Bearer ' + 'EdzG4XoFS4rrtpPs7O9OAIHChXQ=',
            msg           : 'Pvkay+O8L4Wg/1CNGMLhW/S+yC6Ea0VnV5HuBhiqZ58OLQ34hshCPd3NbGINegR45hOdKC3Q8vWlMS0VGQnS1Q=='
        }
    ];
module.exports = authorization;
