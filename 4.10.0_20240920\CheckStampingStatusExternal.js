import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import baseurl from "./Data/baseurl.js";
import tenant from "./Data/tenant-library.js";

let baseurls = baseurl[0];
const url = baseurls.url  + "/services/external/document/checkStampingStatus";
const SLEEP_DURATION = 2;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '10m',
        },
    },
};

// Test scenario
export default function () {
    let tenantKey = tenant[0][1];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': tenantKey.xapikey
        }
    };


    // Get User Profile (user/s/profiles)
    const requestBody = JSON.stringify({

        audit: { callerId: "KELVIN" }
        , refNumber: "TEST-STAMP-VIDA-004"


    });
    const apiResponse = http.post(url, requestBody, requestHeader);
  
    sleep(SLEEP_DURATION);

    check(apiResponse, {
        'is status 200': (r) => r.status === 200

    });

    console.log(apiResponse.body);

}
