/*
    Setiap mau run yang membutuhkan token, run ini API dahulu

    Url: {http://gdkwebsvr:7021/adimobile/esign/oauth/token}
*/

const authorization = [
    [
        {
            // 0
            username      : '<EMAIL>',
            password      : 'Password123!',
            bearerToken   : 'Bearer ' + 'VfE9AvxYP5ZVIA3RstekSQ9zXeU=',
            msg           : 'Pvkay+O8L4Wg/1CNGMLhW/S+yC6Ea0VnV5HuBhiqZ58OLQ34hshCPd3NbGINegR45hOdKC3Q8vWlMS0VGQnS1Q=='
        },
        {
            // 1
            username      : '<PERSON><PERSON><PERSON><PERSON><PERSON>@ADINS',
            password      : 'password',
            bearerToken   : 'Bearer ' + 'LtWc1KVifr+aSVC8kyFJCWLd6d4='
        },
        {
            // 2
            username      : '<EMAIL>',
            password      : 'password',
            bearerToken   : 'Bearer ' + 'KJA1yro6TpDjTR57Bnl13+q+urw='
        },
        {
            // 3
            username      : '<EMAIL>',
            password      : 'Password123!',
            bearerToken   : 'Bearer ' + 'K3zETxKDk4HjWvMqkgpIlKYXCGc='
        }
    ]
];
module.exports = authorization;
