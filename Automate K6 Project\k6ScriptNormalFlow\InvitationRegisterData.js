import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import baseurl from "../data/baseurl.js";
import users from "../data/user.js";

let baseurls = baseurl[0];
const url = baseurls.url + "/services/user/invitationRegisterData";

const SLEEP_DURATION = 5;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            //  ,'Cookie' : 'idjs=FDD47A1F028E0E6900095FC1D1395777'
        }
    };

    let user = users[exec.vu.idInTest - 1];
 
    if (user == null) {
        user = {
        "username": ""
        , "nik": ""
        , "nohp": ""
        };
    }

  
    const requestBody = JSON.stringify({
        audit:{
            callerId: user.username + "@esignhub.my.id"
        },
        tenantCode: 'ADINS',
        msg : "cL86qXFgl9kh/z04bJN+gw=="
    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(response.body);




}
