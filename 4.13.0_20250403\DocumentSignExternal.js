import { check } from 'k6';
import env, {vus} from '../environment.js';
import http from 'k6/http';

const selfieBase64 = open('./Data/selfie-base64.txt');

const documentIds = [
    '00163E03-FD4A-90A0-11F0-1363BED00800',
    '00163E03-FD4A-90A0-11F0-1363BEE90E40',
    '00163E03-FD4A-90A0-11F0-1363BEC09EB0',
    '00163E03-FD4A-90A0-11F0-1363BEF01320',
    '00163E03-FD4A-90A0-11F0-1363BEDD4E70',
    '00163E03-FD4A-90A0-11F0-1363BEFCE460',
    '00163E03-FD4A-90A0-11F0-1363BECCD3B0',
    '00163E03-FD4A-90A0-11F0-1363BECE3340',
    '00163E03-FD4A-90A0-11F0-1363BECC8590',
    '00163E03-FD4A-90A0-11F0-1363BECF6BC0',
    '00163E03-FD4A-90A0-11F0-1363BEDC3D00',
    '00163E03-FD4A-90A0-11F0-1363BEFB5DC0',
    '00163E03-FD4A-90A0-11F0-1363BEE27E90',
    '00163E03-FD4A-90A0-11F0-1363BEED5400',
    '00163E03-FD4A-90A0-11F0-1363BEFCBD50',
    '00163E03-FD4A-90A0-11F0-1363BEE082C0',
    '00163E03-FD4A-90A0-11F0-1363BEED05E0',
    '00163E03-FD4A-90A0-11F0-1363BEF4CE10',
    '00163E03-FD4A-90A0-11F0-1363BED78210',
    '00163E03-FD4A-90A0-11F0-1363BED16790'
];

export let options = env.loadTestOptions;

export default function() {

    const currentIteration = __ITER;
    const currentVus = __VU;
    const index = (currentIteration * vus) + (currentVus - 1);
    const email = '<EMAIL>';

    const url = env.baseUrl + '/services/external/document/signDocument';

    const payload = JSON.stringify({
        audit: {
            callerId: email
        },
        documentId: [ documentIds[index] ],
        email: email,
        phoneNo: '08111128600',
        password: 'AdIns2022!',
        selfPhoto: selfieBase64,
        ipAddress: '127.0.0.1',
        browserInfo: 'Chrome'
    });

    const param = {
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'x-api-key': 'Gy6Ka5XhgRmq@ADINS'
        }
    };

    const response = http.post(url, payload, param);
    const responseBody = response.json();
    
    check(response, {
        'HTTP code 200' : (resp) => resp.status === 200,
    });
    check(responseBody, {
        'Status code 0' : (body) => body.status.code === 0
    });
    
    console.log(response.body);
}