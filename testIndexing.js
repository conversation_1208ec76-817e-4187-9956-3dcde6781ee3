import userManagementData from "./master script/Data/userManagementData.js";

// Test indexing to verify data bounds
export default function () {
    console.log("=== Testing User Data Indexing ===");
    console.log(`Total users available: ${userManagementData.totalUsers}`);
    
    // Test first few users
    console.log("\nFirst 5 users:");
    for (let i = 0; i < 5; i++) {
        const user = userManagementData.getUserByIndex(i);
        if (user) {
            console.log(`  Index ${i}: LoginId=${user.loginId}, FullName=${user.fullName}`);
        } else {
            console.log(`  Index ${i}: No data found`);
        }
    }
    
    // Test last few users
    console.log("\nLast 5 users:");
    for (let i = userManagementData.totalUsers - 5; i < userManagementData.totalUsers; i++) {
        const user = userManagementData.getUserByIndex(i);
        if (user) {
            console.log(`  Index ${i}: LoginId=${user.loginId}, FullName=${user.fullName}`);
        } else {
            console.log(`  Index ${i}: No data found`);
        }
    }
    
    // Test beyond bounds
    console.log("\nTesting beyond bounds:");
    const beyondIndex = userManagementData.totalUsers;
    const beyondUser = userManagementData.getUserByIndex(beyondIndex);
    if (beyondUser) {
        console.log(`  Index ${beyondIndex}: LoginId=${beyondUser.loginId}, FullName=${beyondUser.fullName}`);
    } else {
        console.log(`  Index ${beyondIndex}: No data found (expected)`);
    }
    
    // Test problematic indexes from the error
    console.log("\nTesting problematic indexes:");
    const problemIndexes = [1009612, 1009614, 1009615];
    problemIndexes.forEach(index => {
        const user = userManagementData.getUserByIndex(index);
        if (user) {
            console.log(`  Index ${index}: LoginId=${user.loginId}, FullName=${user.fullName}`);
        } else {
            console.log(`  Index ${index}: No data found (expected - beyond bounds)`);
        }
    });
    
    // Calculate what the max index should be for different VU configurations
    console.log("\nCalculating max indexes for different configurations:");
    
    // Original batch config: 50 VUs * 20000 iterations
    const maxIndexBatch = (50 - 1) * 20000 + (20000 - 1);
    console.log(`  Batch config (50 VUs * 20000 iter): Max index = ${maxIndexBatch}`);
    
    // High perf config: 100 VUs * 10000 iterations  
    const maxIndexHighPerf = (100 - 1) * 10000 + (10000 - 1);
    console.log(`  High perf config (100 VUs * 10000 iter): Max index = ${maxIndexHighPerf}`);
    
    // Safe config: shared-iterations
    console.log(`  Safe config (shared-iterations): Max index = ${userManagementData.totalUsers - 1}`);
    
    console.log("\n=== Indexing Test Complete ===");
}

export let options = {
    vus: 1,
    iterations: 1,
};
