import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./authorization-library.js";
import listDocument from "./listdocument-library.js";

const URL = "https://esignhub.docsol.id/adimobile/esign/services/embed/document/checkDocumentBeforeSigningEmbed";
const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 10,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {

    let auths = auth[0][0];
    let listDoc = listDocument[0][exec.vu.idInInstance - 1];

    const paramsCheckDocumentBeforeSigningEmbed = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
        }
    };

    const generateCheckDocumentBeforeSigningEmbed = JSON.stringify({

        msg: auths['msg'],
        tenantCode: "ADINS",
        vendorCode : "VIDA",
        listDocumentId: [
            listDoc.encryptDocumentByDocD
        ],
        audit: {
            callerId: "default"
        }

    });
    
    const checkCheckDocumentBeforeSigningEmbed = http.post(URL, generateCheckDocumentBeforeSigningEmbed, paramsCheckDocumentBeforeSigningEmbed);

    sleep(SLEEP_DURATION);

    check(checkCheckDocumentBeforeSigningEmbed, {
        'is status 200': (r) => r.status === 200
    });

    console.log([exec.vu.idInTest - 1], checkCheckDocumentBeforeSigningEmbed.body);

}
