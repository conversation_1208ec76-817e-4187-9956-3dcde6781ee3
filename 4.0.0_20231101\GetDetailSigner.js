import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';

import auth from "./Data/authorization-library.js";

const url = "http://gdkwebsvr:7021/adimobile/esign/services/user/s/getSignerDetail";
const SLEEP_DURATION = 2;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 5,
            iterations: 1,
            maxDuration: '10m',
        },
    },
};

// Test scenario
export default function () {
    let auths = auth[0][0];
    const requestHeaeder = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'Authorization': auths['bearerToken']
            //  ,'Cookie' : 'idjs=FDD47A1F028E0E6900095FC1D1395777'
        }
    };


    // Get User Profile (user/s/profiles)
    const requestBody = JSON.stringify({

        audit: { callerId: "<EMAIL>" }
        , email: "<EMAIL>"
        , vendorCode: "VIDA"

    });
    const apiResponse = http.post(url, requestBody, requestHeaeder);
  
    sleep(SLEEP_DURATION);

    check(apiResponse, {
        'is status 200': (r) => r.status === 200

    });

    console.log(apiResponse.body);

}
