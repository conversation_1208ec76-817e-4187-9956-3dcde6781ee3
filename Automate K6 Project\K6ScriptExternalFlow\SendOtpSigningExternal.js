import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import baseurl from "../data/baseurl.js";
import users from "../data/user.js";
import tenants from "../data/apiKey.js";


let baseurls = baseurl[0];
const url = baseurls.url + "/services/external/user/sentOtpSigning";
let tenant = tenants[1];
const SLEEP_DURATION = 2;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': tenant.xapikey

        }
    };

    let user = users[exec.vu.idInTest - 1];
 
    if (user == null) {
        user = {
        "username": ""
        , "nik": ""
        , "nohp": ""
        };
    }
  
    const requestBody = JSON.stringify({
        audit:{
            callerId: user.username + "esignhub.my.id"
        },
        email: user.username + "esignhub.my.id",
        refNumber: user.refNumber,
        sendingPointOption : "SMS",
        phoneNo : user.nohp,

    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });
    console.log(response.body);
}