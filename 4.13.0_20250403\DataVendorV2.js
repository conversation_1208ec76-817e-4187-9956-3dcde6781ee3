import { check } from 'k6';
import env from '../environment.js';
import http from 'k6/http';
import {login} from '../basic-function.js';

export let options = env.loadTestOptions;
export let username = '<EMAIL>';
export let password = 'Password123!';

export function setup() {
    const token = login(env.baseUrl, username, password);
    return { token: token };
}

export default function(data) {

    const url = env.baseUrl + '/services/data/s/vendorV2';

    const payload = JSON.stringify({
        audit: {
            callerId: username
        },
        tenantCode: 'ADINS'
    });

    const param = {
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': 'Bearer ' + data.token
        }
    };

    const response = http.post(url, payload, param);
    const responseBody = response.json();

    check(response, {
        'HTTP code 200' : (resp) => resp.status === 200,
    });
    check(responseBody, {
        'Status code 0' : (body) => body.status.code === 0
    });

    // console.log(response.body);
}