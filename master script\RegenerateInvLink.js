import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./Data/authorization-library.js";
import baseurl from "./Data/baseurl.js";



let baseurls = baseurl[0];
const url = baseurls.url + "/services/user/s/generateInvitationLinkECertExpired";

const SLEEP_DURATION = 1;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {
    let auths = auth[3];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'Authorization': 'Bearer yrjsaZ2LMvEEVlBXDdgH+QWkCLw='

        }
    };

  
    const requestbody = JSON.stringify({

        audit: { callerId:"<EMAIL>" },
        documentId: "00163E04-1D85-A9D7-11EF-1290FFD64DC0",
        loginId: "<EMAIL>",
        
    }, null, "\t");

    const response = http.post(url, requestbody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });
    sleep(SLEEP_DURATION);

    console.log(response.status);




}
