import json
import psycopg2
from psycopg2 import sql
from dotenv import load_dotenv
import os
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import base64

# Load environment variables
load_dotenv()

def getNewestCanStampDocument():
    db_config = {
        'dbname': os.getenv('dataSourceName'),
        'user': os.getenv('dataSourceUsername'),
        'password': os.getenv('dataSourcePassword'),
        'host': os.getenv('dataSourceUrl'),
        'port': os.getenv('dataSourcePort')
    }

    try:
        connection = psycopg2.connect(**db_config)
        cursor = connection.cursor()

        query = '''
        SELECT ref_number, document_id
        FROM tr_document_h tdh 
        JOIN tr_document_d tdd ON tdh.id_document_h = tdd.id_document_h 
        WHERE tdh.automatic_stamping_after_sign = '0' 
          AND proses_materai = '0' 
          AND tdh.id_ms_tenant = 5
          and tdh.is_active  = '1'
          AND tdd.total_sign = tdd.total_signed 
          and ((tdd.total_materai  - tdd.total_stamping) != 0)
        ORDER BY tdh.dtm_crt DESC 
        LIMIT 20
        '''

        cursor.execute(query)
        result = cursor.fetchall()

        return [(row[0], row[1]) for row in result] if result else []

    except psycopg2.Error as e:
        print(f"Database error: {e}")
        return []
    finally:
        if connection:
            cursor.close()
            connection.close()

def getAesKey():
    db_config = {
        'dbname': os.getenv('dataSourceName'),
        'user': os.getenv('dataSourceUsername'),
        'password': os.getenv('dataSourcePassword'),
        'host': os.getenv('dataSourceUrl'),
        'port': os.getenv('dataSourcePort')
    }

    try:
        connection = psycopg2.connect(**db_config)
        cursor = connection.cursor()

        query = '''
        SELECT aes_encrypt_key 
        FROM ms_tenant
        WHERE tenant_code = 'ADINS'
        '''
        cursor.execute(query)

        result = cursor.fetchone()

        if result:
            return result[0]
        else:
            print("AES key not found.")
            return None

    except (Exception, psycopg2.DatabaseError) as error:
        print(f"Error occurred: {error}")
        return None
    finally:
        if connection:
            cursor.close()
            connection.close()

def encrypt(data, key):
    try:
        cipher = AES.new(key.encode("utf-8"), AES.MODE_ECB)
        plaintext = data.encode("utf-8")
        padded_plaintext = pad(plaintext, AES.block_size)
        encrypted_plaintext = cipher.encrypt(padded_plaintext)
        return base64.b64encode(encrypted_plaintext).decode("utf-8")
    except Exception as e:
        print(f"Encryption error: {e}")
        return None

def replaceStampDocumentJs():
    try:
        new_documents = getNewestCanStampDocument()
        key = getAesKey()

        if not key:
            print("No AES key available. Aborting.")
            return

        stamp_documents = [
            {
                "refNumber": ref_number,
                "documentId": document_id,
                "encryptedDocId": encrypt(document_id, key)
            }
            for ref_number, document_id in new_documents
        ]

        with open('data/stampDocumentId.js', 'w') as file:
            file.write("const stampDocument = \n")
            file.write(json.dumps(stamp_documents, indent=4))
            file.write(";\n")
            file.write("module.exports = stampDocument;\n")

        print("File replaced successfully.")

    except Exception as e:
        print(f"Error replacing document: {e}")

# replaceStampDocumentJs()
