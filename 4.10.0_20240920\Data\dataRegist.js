/*
    <PERSON><PERSON><PERSON> mau run yang membutuhkan token, run ini API dahulu

    Url: {http://gdkwebsvr:7021/adimobile/esign/oauth/token}
*/

const dataRegist = 
    [
        {
        "msg": "lkyhp6",
        "name": "USERHHJH",
        "date": "1974-06-27",
        "nik": "3511002706747797",
        "phone": "08120027674"
        },
        {
        "msg": "cxhHdx",
        "name": "USERCEJC",
        "date": "1965-07-16",
        "nik": "3511001607652492",
        "phone": "08120016765"
        },
        {
        "msg": "ycVlBl",
        "name": "USERBBG<PERSON>",
        "date": "1967-08-25",
        "nik": "3511002508671165",
        "phone": "08120025867"
        },
        {
        "msg": "bhg5az",
        "name": "USERAHBD",
        "date": "1973-09-06",
        "nik": "3511000609730713",
        "phone": "0812006973"
        },
        {
        "msg": "m0Rbuf",
        "name": "USERFCEG",
        "date": "1978-01-26",
        "nik": "3511002601785246",
        "phone": "08120026178"
        },
        {
        "msg": "Ajkasl",
        "name": "USERDEFH",
        "date": "1982-08-28",
        "nik": "3511002808823457",
        "phone": "08120028882"
        },
        {
        "msg": "ziPcIt",
        "name": "USERDJCC",
        "date": "1990-12-12",
        "nik": "3511001212903922",
        "phone": "081200121290"
        },
        {
        "msg": "k6svnm",
        "name": "USERADFA",
        "date": "1963-03-31",
        "nik": "3511003103630350",
        "phone": "08120031363"
        },
        {
        "msg": "rEAypd",
        "name": "USERFAHD",
        "date": "1981-08-06",
        "nik": "3511000608815073",
        "phone": "0812006881"
        },
        {
        "msg": "ujuX1l",
        "name": "USERFBDB",
        "date": "1973-06-11",
        "nik": "3511001106735131",
        "phone": "08120011673"
        },
        {
        "msg": "ahaury",
        "name": "USERCCJC",
        "date": "1985-11-08",
        "nik": "3511000811852292",
        "phone": "08120081185"
        },
        {
        "msg": "dckudU",
        "name": "USERHEGJ",
        "date": "1989-04-15",
        "nik": "3511001504897469",
        "phone": "08120015489"
        },
        {
        "msg": "rjcitn",
        "name": "USERDECG",
        "date": "1984-04-13",
        "nik": "3511001304843426",
        "phone": "08120013484"
        },
        {
        "msg": "geeCLv",
        "name": "USERAIJG",
        "date": "1986-11-23",
        "nik": "3511002311860896",
        "phone": "081200231186"
        },
        {
        "msg": "8pitlm",
        "name": "USERFABD",
        "date": "1979-03-28",
        "nik": "3511002803795013",
        "phone": "08120028379"
        },
        {
        "msg": "Jebqau",
        "name": "USEREICC",
        "date": "1984-09-27",
        "nik": "3511002709844822",
        "phone": "08120027984"
        },
        {
        "msg": "xzwuu9",
        "name": "USEREFIE",
        "date": "1979-12-25",
        "nik": "3511002512794584",
        "phone": "081200251279"
        },
        {
        "msg": "vdwmcb",
        "name": "USERBEJJ",
        "date": "1970-02-09",
        "nik": "3511000902701499",
        "phone": "0812009270"
        },
        {
        "msg": "izGJgg",
        "name": "USERCEHH",
        "date": "1983-11-14",
        "nik": "3511001411832477",
        "phone": "081200141183"
        },
        {
        "msg": "qpPeia",
        "name": "USERIGGB",
        "date": "1979-04-07",
        "nik": "3511000704798661",
        "phone": "0812007479"
        }
    ]
module.exports = dataRegist;
