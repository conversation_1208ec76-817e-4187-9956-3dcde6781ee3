import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./authorization-library.js";

import baseurl from "./Data/baseurl.js";



let baseurls = baseurl[0];

const url =  baseurls + "/services/user/s/verifyLivenessFaceCompare";
const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 5,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {

    let auths = auth[0][3];

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'Authorization': auths['bearerToken']
        }
    };

    const requestBody = JSON.stringify({

        audit: {
            callerId: "<EMAIL>"
        },
        tenantCode: "ADINS",
        email : "",
        vendorCode : "",
        img1 : "",
    });
    
    const response = http.post(URL, requestBody, requestHeader);

    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200
    });

    console.log([exec.vu.idInTest - 1], response.body);

}
