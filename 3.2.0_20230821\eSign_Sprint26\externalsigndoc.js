import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./Data/authorization-library.js";
import listDocument from "./Data/listdocument-library.js";

const URL = "http://gdkwebsvr:7021/adimobile/esign/services/external/document/signDocument";

// const URL = "http://localhost:8095/services/document/s/checkDocumentBeforeSigning";
const SLEEP_DURATION = 1;

// setup per iteration
// export let options = {
//     discardResponseBodies: false,
//     scenarios: {

//         contacts: {
//             executor: 'per-vu-iterations',
//             vus: 10,
//             iterations: 1,
//             maxDuration: '1m',
//         },
//     },
// };

export let options = {
    vus: 10,
  duration: '30s'
};

// Test scenario
export default function () {

    let auths = auth[0][0];
    let listDoc = listDocument[0][exec.vu.idInInstance - 1];

    const paramsCheckVerificationStatus = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': 'Gy6Ka5XhgRmq@ADINS'
        }
    };

    const generateCheckVerificationStatus = JSON.stringify({

        
        audit: {
            callerId: "default"
        },
        documentId: [listDoc.documentId]
        ,email : "<EMAIL>"
        ,password : "Super123!"
        ,ipAddress : "***********"
        , browserInfo : "Browser Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
        ,otp : "12345"
        ,selfPhoto : ""

    });
    
    const CheckCheckVerificationStatus = http.post(URL, generateCheckVerificationStatus, paramsCheckVerificationStatus);

    sleep(SLEEP_DURATION);

    check(CheckCheckVerificationStatus, {
        'is status 200': (r) => r.status === 200
    });

    console.log( CheckCheckVerificationStatus.body);

}
