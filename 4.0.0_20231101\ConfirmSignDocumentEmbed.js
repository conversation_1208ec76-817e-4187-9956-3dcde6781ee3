import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';

import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const url = baseurls + "/embed/document/signConfirmDocument";
const SLEEP_DURATION = 1;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '10m',
        },
    },
};

// Test scenario
export default function () {

    const paramsRegistration = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
          
        }
    };

    // Random e-mail suffix


 

    const registerrequest = JSON.stringify({

        audit: { callerId: "<EMAIL>" },
        msg: "Pvkay+O8L4Wg/1CNGMLhW21BjlToc9IcZxoHcrGf/mjCftTmIwn0Mb9lh9RYLf/fswyir60BfJ3S+LjNhstnLA==",
    tenantCode: "WOMF",
    browser: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    ipAddress : "*************",
    documentIds : [
        "CN48HtkuYOR7kyE4BdAo2LUJZ4bF7uRyOt22M/W2MNOU6b2M1gC+0tUGvbPHHrDb",
        "CZF2V0Dv6Ji+mIvg7hAwXvk1VslVGfdIUHzzX7guI1ECFpTi0eDcUY5EnbvpeYQp"
    ],
    }, null, "\t");

    
    const registerResponse = http.post(url, registerrequest, paramsRegistration);
    check(registerResponse, {
        'is status 200': (r) => r.status === 200,

    });

    sleep(SLEEP_DURATION);


}
