import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import userManagementData from "./Data/userManagementData.js";
import baseurl from "./Data/baseurl.js";
import authorization from "./Data/authorization-library.js";

// Configuration
let baseurls = baseurl[0];
const url = baseurls.url + "/services/user-management/s/insertUserManagement";
const SLEEP_DURATION = 0.5; // Slightly longer sleep for batch processing

// Use <EMAIL> for authorization (index 3)
const authData = authorization[3];

// Conservative batch configuration for stability
export let options = {
    discardResponseBodies: false,
    scenarios: {
        insert_users_batch: {
            executor: 'per-vu-iterations',
            vus: 50,            // 50 virtual users
            iterations: 20000,  // 20,000 iterations per VU = 1,000,000 total
            maxDuration: '180m', // 3 hours max duration
        },
    },
    thresholds: {
        http_req_duration: ['p(95)<10000'], // 95% of requests should be below 10s
        http_req_failed: ['rate<0.05'],     // Error rate should be below 5%
    },
};

// Test scenario
export default function () {
    // Calculate global user index
    const globalIndex = (exec.vu.idInTest - 1) * 20000 + exec.scenario.iterationInTest;
    
    // Get user data by global index
    const userData = userManagementData.getUserByIndex(globalIndex);
    
    if (!userData) {
        console.error(`No user data found for index ${globalIndex}`);
        return;
    }

    // Request headers
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': authData.bearerToken
        }
    };

    // Request body based on the provided cURL
    const requestBody = JSON.stringify({
        "audit": {
            "callerId": "<EMAIL>"
        },
        "fullName": userData.fullName,
        "tenantCode": "ADINS",
        "loginId": userData.loginId,
        "roleCode": "USER_EDITOR",
        "password": "ADINS",
        "officeCode": "0191"
    });

    // Add delay to avoid overwhelming the server
    sleep(SLEEP_DURATION);

    // Make the request with retry logic
    let response;
    let retryCount = 0;
    const maxRetries = 3;

    do {
        response = http.post(url, requestBody, requestHeader);
        
        // If successful, break out of retry loop
        if (response.status === 200) {
            break;
        }
        
        // If it's a client error (4xx), don't retry
        if (response.status >= 400 && response.status < 500 && response.status !== 429) {
            break;
        }
        
        // For server errors or rate limiting, wait and retry
        if (retryCount < maxRetries && (response.status >= 500 || response.status === 429)) {
            retryCount++;
            const backoffTime = Math.min(60, Math.pow(2, retryCount) + Math.random() * 5);
            console.log(`Retry ${retryCount}/${maxRetries} for user ${globalIndex + 1} after ${backoffTime.toFixed(1)}s`);
            sleep(backoffTime);
        }
        
    } while (retryCount < maxRetries && (response.status >= 500 || response.status === 429));

    // Check response
    const isSuccess = check(response, {
        'is status 200': (r) => r.status === 200,
        'response has success status': (r) => {
            try {
                const body = JSON.parse(r.body);
                return body.status && body.status.code === 0;
            } catch (e) {
                return false;
            }
        }
    });

    // Log progress every 50 requests
    if (globalIndex % 50 === 0) {
        console.log(`Progress: User ${globalIndex + 1}/${userManagementData.totalUsers} (${((globalIndex + 1) / userManagementData.totalUsers * 100).toFixed(2)}%) - LoginId: ${userData.loginId}, Status: ${response.status}`);
    }

    // Log errors for debugging
    if (!isSuccess) {
        console.error(`Failed to insert user ${globalIndex + 1} (${userData.loginId}): Status ${response.status}, Body: ${response.body.substring(0, 200)}`);
    }

    // Additional logging for specific error cases
    if (response.status === 400) {
        try {
            const errorBody = JSON.parse(response.body);
            if (errorBody.status && errorBody.status.message) {
                console.log(`Business logic error for user ${globalIndex + 1}: ${errorBody.status.message}`);
            }
        } catch (e) {
            // Ignore JSON parse errors
        }
    }
}

// Setup function to validate data availability
export function setup() {
    console.log("=== InsertUserManagement Batch Load Test Setup ===");
    console.log(`Target URL: ${url}`);
    console.log(`Total users to insert: ${userManagementData.totalUsers}`);
    console.log(`Authorization: ${authData.username}`);
    console.log(`VUs: ${options.scenarios.insert_users_batch.vus}`);
    console.log(`Iterations per VU: ${options.scenarios.insert_users_batch.iterations}`);
    console.log(`Total requests: ${options.scenarios.insert_users_batch.vus * options.scenarios.insert_users_batch.iterations}`);
    console.log(`Estimated duration: ${(options.scenarios.insert_users_batch.vus * options.scenarios.insert_users_batch.iterations * SLEEP_DURATION / 60).toFixed(0)} minutes`);
    
    // Validate that we have enough user data
    const totalRequests = options.scenarios.insert_users_batch.vus * options.scenarios.insert_users_batch.iterations;
    if (userManagementData.totalUsers < totalRequests) {
        throw new Error(`Insufficient user data. Need ${totalRequests} users, but only have ${userManagementData.totalUsers}`);
    }
    
    // Test a sample request to validate configuration
    console.log("Testing sample request...");
    const sampleUser = userManagementData.getUserByIndex(0);
    if (!sampleUser) {
        throw new Error("No sample user data available");
    }
    
    console.log(`Sample user: LoginId=${sampleUser.loginId}, FullName=${sampleUser.fullName}`);
    console.log("Setup complete. Starting batch load test...");
    return {};
}

// Teardown function for final reporting
export function teardown(data) {
    console.log("=== InsertUserManagement Batch Load Test Complete ===");
    console.log("Check the K6 summary for detailed metrics.");
    console.log("Review logs for any failed insertions that may need manual retry.");
}
