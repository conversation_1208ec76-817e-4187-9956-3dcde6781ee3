import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import baseurl from "../data/baseurl.js";
import users from "../data/user.js";

let baseurls = baseurl[0];
const url = baseurls.url  + "/services/embed/user/verifyOtpSigningEmbed";
const SLEEP_DURATION = 2;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '10m',
        },
    },
};

// Test scenario
export default function () {
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
        }
    };

    let user = users[exec.vu.idInTest - 1];
 
    if (user == null) {
        user = {
        "username": ""
        , "nik": ""
        , "nohp": ""
        };
    }

    // Get User Profile (user/s/profiles)
    const requestBody = JSON.stringify({

        audit: { callerId: user.username + "@esignhub.my.id" },
        msg: user.msg,
        tenantCode: "ADINS",
        phoneNo: user.nohp,
        otpCode: user.otp,
        vendorCode: "VIDA",
        documentId: [user.documentId]
    });
    const response = http.post(url, requestBody, requestHeader);
  
    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200

    });

    console.log(response.body + response.status);

}
