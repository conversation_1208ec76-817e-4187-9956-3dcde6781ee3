import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./Data/authorization-library.js"
import baseurl from "./Data/baseurl.js";
import tenant from "./Data/tenant-library.js";


let baseurls = baseurl[0];
const url = baseurls.url + "/services/document/resendSignNotif";

const SLEEP_DURATION = 5;

let authData = auth[1];

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {
    let tenantKey = tenant[0][1];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            //  ,'Cookie' : 'idjs=FDD47A1F028E0E6900095FC1D1395777'
        }
    };

  




    const requestBody = JSON.stringify({
        msg: "+EH8s0REprgUGpj94nKz5cfrobCK/QmA6MEcXUWTzyTkMC5k0Z/vC/aFnrDRLNoenF+TBfTskcriC7dHvxeK6OhhPvnT2gaeyTY6l4c6BGVGrUPft+EUfYeQ1kivpEjDLepkOdxVDVMfliECKGUCZg==",
        documentId : "uqP++LoGQaBZzc4K9wCCJew/2IEjjjfZA0k8FHl4zmUA4Jo+pP3LtWJG3cbrf7h3",
        tenantCode: "ADINS",
        audit: {
            callerId: "<EMAIL>"
        }
    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(response.body);




}
