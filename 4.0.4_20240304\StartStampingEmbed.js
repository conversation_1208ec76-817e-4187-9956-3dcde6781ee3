import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import refNumber from "./Data/refNumber.js";
import baseurl from "./Data/baseurl.js";
import tenant from "./Data/tenant-library.js";


let baseurls = baseurl[0];
const url = baseurls.url + "/services/embed/document/startStampingMateraiEmbed";

const SLEEP_DURATION = 5;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {
    let tenantKey = tenant[0][1];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
        }
    };

  




    const requestBody = JSON.stringify(
        {
            tenantCode:"ADINS",
            refNumber:refNumber[exec.vu.idInTest - 1],
            msg:"zV8OBa9gryd8t6t/IX/eaTLD7GgrjdMSbniIxxVbjqynkPiCMtf7mI8Al7/j6ayQTeSw18nnSF/6nw6j0/CAMOfRDaVzOoDaizK2+E5nITI6M2eq0+tP/jolzk2MrXmP1n2vMSRmsLqlwDWAmM65HA==",
            audit:{
                callerId:"CONFINS"
            }
        }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(response.body);




}
