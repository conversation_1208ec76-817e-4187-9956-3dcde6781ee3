import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import tenant from "./Data/tenant-library.js";
import auth from "./Data/authorization-library.js";
import  APIS from "./Data/apis.js";
import baseurl from "./Data/baseurl.js"

let baseurls = baseurl[0]
const url = baseurls.url  + "/services/user/generateInvitationLinkV2";
const SLEEP_DURATION = 2;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 2,
            iterations: 1,
            maxDuration: '10m',
        },
    },
};

// Test scenario
export default function () {
    let auths = auth[0][0];
    let tnt = tenant[0][0];
    const requestHeaeder = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': tnt['xapikey']
        }
    };
// Random e-mail suffix

let apis = APIS[0][exec.vu.idInTest - 1];

if (apis == null) {
    apis = {
        "username": ""
        , "nik": ""
        , "nohp": ""
    };
}

    // Get User Profile (user/s/profiles)
    const requestBody = JSON.stringify({
        audit: { callerId: apis.username }
        , tenantCode: "ADINS"
        , users: [{
          nama: apis.username,
          tlp: apis.nohp  + [exec.vu.idInTest -1],
          email: "MALVINTEST004" + [exec.vu.idInTest -1] + "@esignhub.my.id",
          jenisKelamin: "M",
          tmpLahir: "BOGOR",
          tglLahir: "1980-01-01",
          idKtp: apis.nik,
          provinsi: "Jawa Barat",
          kota: "Bogor",
          kecamatan: "Bogor Selatan",
          kelurahan: "Baranangsiang",
          kodePos: "16143",
          alamat: "JL. SAWOKNA NO.1000 BANTAR KEMANG"
          , vendorCode: "PRIVY"
        }]

    });
    const apiResponse = http.post(url, requestBody, requestHeaeder);
  
    sleep(SLEEP_DURATION);

    check(apiResponse, {
        'is status 200': (r) => r.status === 200

    });

    console.log(apiResponse.body);

}
