export const vus = 20;
export const iterations = 1;

const env = {
    baseUrl: 'http://localhost:8095',
    // baseUrl: 'http://gdkwebsvr:7021/adimobile/esign',
    // baseUrl: 'https://esignhub.ad-ins.com/adimobile/esign',
    loadTestOptions: {
        discardResponseBodies: false,
        scenarios: {
            contacts: {
                executor: 'per-vu-iterations',
                vus: vus,
                iterations: iterations,
                maxDuration: '30m'
            }
        }
    },
    stressTestOptions: {
        discardResponseBodies: false,
        scenarios: {
            contacts: {
                executor: 'constant-vus',
                vus: 20,
                maxDuration: '10m'
            }
        }
    }
}

export default env;