import random
import json

def generate_random_number():
        randomNumber = "351100"
        randomNumber += str(random.randint(1, 31)).zfill(
            2
        )
        randomNumber += str(random.randint(1, 12)).zfill(
            2
        )
        randomNumber += str(random.randint(60, 90)).zfill(
            2
        )
        randomNumber += str(random.randint(0, 9999)).zfill(
            4
        )
        return randomNumber

def generateMockData():
    userMockList = []
    for _ in range(20):
        randomNIK = generate_random_number()

        day = int(randomNIK[6:8])
        month = int(randomNIK[8:10])
        year = int(randomNIK[10:12])

        randomPhone = "081200"
        randomPhone += str(day)
        randomPhone += str(month)
        randomPhone += str(year)

        date = f"{year + 1900:04d}-{month:02d}-{day:02d}"

        alphabet_mapping = "ABCDEFGHIJ"

        mapped_digits = "".join(
            [alphabet_mapping[int(digit)] for digit in randomNIK[-4:]]
        )
        user = "User" + mapped_digits
    
        userInfo = {
            "nik": randomNIK,
            "date": date,
            "username": user,
            "nohp": randomPhone,
            "msg" : "",
            "otp" : "",
            "documentId" : "",
            "refNumber" : "",
            "idSigningProcessAuditTrailDetail" : "",
            "bearerToken" : ""
        }
        userMockList.append(userInfo)

    return userMockList

def createUser() :

    user = generateMockData()

    jsonString = json.dumps(user, indent=4)
    listUser = jsonString.replace('}, {', '},\n{')
    
    with open('data/user.js', 'w') as file:
        file.write("const user = \n")
        file.write(listUser)
        file.write(";\nmodule.exports = user;\n")

# createUser()