import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./Data/authorization-library.js";
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const url = baseurls.url  + "/services/embed/user/generateInvitationLinkECertExpired";
const SLEEP_DURATION = 2;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '10m',
        },
    },
};

// Test scenario
export default function () {
    let auths = auth[5];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
        }
    };


    // Get User Profile (user/s/profiles)
    const requestBody = JSON.stringify({

        audit: { callerId: "<EMAIL>" }
        , msg: auths.msg
        , tenantCode: "ADINS"
        , documentId : "L2JAIgA2E94s3hD07poYGj12zFspylFBLeteIVDKoMFRnKzwCRRj3hSwG7SDQPDu"


    });
    const apiResponse = http.post(url, requestBody, requestHeader);
  
    sleep(SLEEP_DURATION);

    check(apiResponse, {
        'is status 200': (r) => r.status === 200

    });

    console.log(apiResponse.body);

}
