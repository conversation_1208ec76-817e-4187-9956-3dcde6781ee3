import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./Data/authorization-library.js";
import baseurl from "./Data/baseurl.js";



let baseurls = baseurl[0];
const url = baseurls.url + "/services/messageDelivery/s/listMessageDelivery";

const SLEEP_DURATION = 5;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {
    let auths = auth[3];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'Authorization': auths['bearerToken']

        }
    };

  
    const requestbody = JSON.stringify({

        audit: { callerId:"<EMAIL>" },
        deliveryStatus: "",
        messageMedia: "",
        page: 1,
        recipient: "",
        reportTimeEnd: "",
        reportTimeStart: "",
        requestTimeEnd: "",
        requestTimeStart: "",
        tenantCode: "ADINS",
        vendorCode: ""
    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestbody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(response.body);




}
