import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./authorization-library.js";
import listDocument from "./listdocument-library.js";

const URL = "http://localhost:8095/services/embed/document/signConfirmDocument";
const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 10,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {

    let auths = auth[0][0];
    let listDoc = listDocument[0][exec.vu.idInInstance - 1];

    const paramsSignConfirmDokumenEmbed = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
        }
    };

    const generateSignConfirmDokumenEmbed = JSON.stringify({

        audit: {
            callerId: "default"
        },
        msg: auths['msg'],
        tenantCode: "WOMF",
        ipAddress: "************",
        browser: "mozila",
        documentIds: [
            listDoc.encryptDocumentByDocD
        ]

    });
    
    const checkSignConfirmDokumenEmbed = http.post(URL, generateSignConfirmDokumenEmbed, paramsSignConfirmDokumenEmbed);

    sleep(SLEEP_DURATION);

    check(checkSignConfirmDokumenEmbed, {
        'is status 200': (r) => r.status === 200
    });

    console.log([exec.vu.idInTest - 1], checkSignConfirmDokumenEmbed.body);

}
