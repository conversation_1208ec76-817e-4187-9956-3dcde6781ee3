import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import baseurl from "./Data/baseurl.js";
import unsignDocument from "./Data/unsignDocumentId.js";
import authorizations from "./Data/authorization-library.js";

let baseurls = baseurl[0];
const URL = baseurls.url  + "/services/embed/document/checkDocumentBeforeSigningEmbed";

const SLEEP_DURATION = 2;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {

    let listDoc = unsignDocument[(exec.vu.idInInstance - 1) % unsignDocument.length];
    let authorization = authorizations[0];

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
          
        }
    };

    const requestBody = JSON.stringify({

        msg: authorization.msg,
        listDocumentId: [
            listDoc.encryptedDocId
        ],
        audit: {
            callerId: "default"
        },
        tenantCode: "ADINS",
        vendorCode : "VIDA"


    });
    
    const response = http.post(URL, requestBody, requestHeader);

    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200
    });

     console.log(response.body);

}
