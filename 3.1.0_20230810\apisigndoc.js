import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';

const URL = "http://localhost:8095/services/document/s/signConfirmDokumen";
const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 1,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {

    const paramsSignDoc = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'Authorization': 'Bearer ' + '/QhIKzfuCUaakTS5J7DKw82kukk='
        }
    };

    const generateSignDoc = JSON.stringify({

        audit: {
            callerId: "default"
        },
        email: "<EMAIL>",
        ipAddress: "************",
        browser: "mozila",
        documentId: [
            "00155D0B-7502-BB44-11EE-2537A4E4A160"
        ]
        
    });
    
    const checkSignDoc = http.post(URL, generateSignDoc, paramsSignDoc);

    sleep(SLEEP_DURATION);

    check(checkSignDoc, {
        'is status 200': (r) => r.status === 200
    });

    console.log([exec.vu.idInTest - 1], checkSignDoc.body);

}
