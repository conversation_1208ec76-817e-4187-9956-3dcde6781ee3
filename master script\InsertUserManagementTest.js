import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import userManagementData from "./Data/userManagementData.js";
import baseurl from "./Data/baseurl.js";
import authorization from "./Data/authorization-library.js";

// Configuration
let baseurls = baseurl[0];
const url = baseurls.url + "/services/user-management/s/insertUserManagement";
const SLEEP_DURATION = 1; // 1 second sleep for testing

// Use <EMAIL> for authorization (index 3)
const authData = authorization[3];

// Small test configuration - only 10 users for testing
export let options = {
    discardResponseBodies: false,
    scenarios: {
        test_insert_users: {
            executor: 'per-vu-iterations',
            vus: 2,           // 2 virtual users
            iterations: 5,    // 5 iterations per VU = 10 total
            maxDuration: '5m', // 5 minutes max duration
        },
    },
    thresholds: {
        http_req_duration: ['p(95)<10000'], // 95% of requests should be below 10s
        http_req_failed: ['rate<0.2'],      // Error rate should be below 20% for testing
    },
};

// Test scenario
export default function () {
    // Calculate global user index
    const globalIndex = (exec.vu.idInTest - 1) * 5 + exec.scenario.iterationInTest;
    
    // Get user data by global index
    const userData = userManagementData.getUserByIndex(globalIndex);
    
    if (!userData) {
        console.error(`No user data found for index ${globalIndex}`);
        return;
    }

    console.log(`Testing user ${globalIndex + 1}: LoginId=${userData.loginId}, FullName=${userData.fullName}`);

    // Request headers
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': authData.bearerToken
        }
    };

    // Request body based on the provided cURL
    const requestBody = JSON.stringify({
        "audit": {
            "callerId": "<EMAIL>"
        },
        "fullName": userData.fullName,
        "tenantCode": "ADINS",
        "loginId": userData.loginId,
        "roleCode": "USER_EDITOR",
        "password": "ADINS",
        "officeCode": "0191"
    });

    console.log(`Request body: ${requestBody}`);

    // Add delay
    sleep(SLEEP_DURATION);

    // Make the request
    const response = http.post(url, requestBody, requestHeader);

    console.log(`Response status: ${response.status}`);
    console.log(`Response body: ${response.body}`);

    // Check response
    const isSuccess = check(response, {
        'is status 200': (r) => r.status === 200,
        'response has success status': (r) => {
            try {
                const body = JSON.parse(r.body);
                return body.status && body.status.code === 0;
            } catch (e) {
                console.error(`Failed to parse response: ${e.message}`);
                return false;
            }
        }
    });

    // Log results
    if (isSuccess) {
        console.log(`✓ Successfully inserted user ${globalIndex + 1} (${userData.loginId})`);
    } else {
        console.error(`✗ Failed to insert user ${globalIndex + 1} (${userData.loginId}): Status ${response.status}`);
        
        // Try to parse error message
        try {
            const errorBody = JSON.parse(response.body);
            if (errorBody.status && errorBody.status.message) {
                console.error(`Error message: ${errorBody.status.message}`);
            }
        } catch (e) {
            // Ignore JSON parse errors
        }
    }
}

// Setup function to validate data availability
export function setup() {
    console.log("=== InsertUserManagement Test Setup ===");
    console.log(`Target URL: ${url}`);
    console.log(`Authorization: ${authData.username}`);
    console.log(`Bearer Token: ${authData.bearerToken}`);
    console.log(`Total available users: ${userManagementData.totalUsers}`);
    console.log("VUs: 2");
    console.log("Iterations per VU: 5");
    console.log("Total test requests: 10");
    
    // Test data access
    console.log("\nTesting data access...");
    for (let i = 0; i < 3; i++) {
        const testUser = userManagementData.getUserByIndex(i);
        if (testUser) {
            console.log(`  User ${i}: LoginId=${testUser.loginId}, FullName=${testUser.fullName}`);
        } else {
            console.error(`  User ${i}: No data found`);
        }
    }
    
    console.log("Setup complete. Starting test...");
    return {};
}

// Teardown function for final reporting
export function teardown(data) {
    console.log("=== InsertUserManagement Test Complete ===");
    console.log("This was a small test run. Check results before running full load test.");
}
