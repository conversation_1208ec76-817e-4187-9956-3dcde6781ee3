import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import APIS from "./Data/apis.js";
import tenant from "./Data/tenant-library.js"
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const url = baseurls.url +"/oauth/token";
const SLEEP_DURATION = 2;

export let options = {
  discardResponseBodies: false,
  scenarios: {

   contacts: {
      executor: 'per-vu-iterations',
      vus: 1,
      iterations: 1,
      maxDuration: '10m',
    },
  },
};

// Test scenario
export default function () {

  let tenantKey = tenant[0][1];
  const requestHeader = {
    headers: {
      'Content-Type': 'application/json'
      , 'x-api-key': 'Gy6Ka5XhgRmq@ADINS'
    }
  };

  // Random e-mail suffix

  let apis = APIS[0][exec.vu.idInTest];
 
  if (apis == null) {
    apis = {
      "username": ""
      , "nik": ""
      , "nohp": ""
    };
  }
  // Get User Profile (user/s/profiles)
  const formData = {
    client_id: "frontend",
    grant_type : "password",
    username : "<EMAIL>",
    password : "Password123!"
  };

  // const requestBody = http.formUrlEncode(formData);


  const response = http.post(url, formData);
  sleep(SLEEP_DURATION);

  check(response, {
    'is status 200': (r) => r.status === 200 
    
  });

  console.log(response.body);


}
