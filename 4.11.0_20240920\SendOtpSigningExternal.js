import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import unsignDocument from "./Data/unsignDocumentId.js";
import authorizations from "./Data/authorization-library.js";
import baseurl from "./Data/baseurl.js";
import tenant from "./Data/tenant-library.js";


let baseurls = baseurl[0];
const url = baseurls.url + "/services/external/user/sentOtpSigning";

const SLEEP_DURATION = 2;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {
    
    let tenantKey = tenant[0][1];
    let listDoc = unsignDocument[(exec.vu.idInInstance - 1) % unsignDocument.length];
    let authorization = authorizations[0];

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': tenantKey.xapikey

        }
    };

  
    const requestBody = JSON.stringify({
        audit:{
            callerId: authorization.username
        },
        email:authorization.username,
        refNumber: listDoc.refNumber,
        sendingPointOption : "SMS",
        phoneNo : "08129772389"

    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });
    console.log(response.body);
}