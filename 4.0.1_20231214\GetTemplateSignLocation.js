import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import datauser from './Data/apis.js';
import tenantLibrary from './Data/tenant-library.js';
import baseurl from "./Data/baseurl.js";
import docTemplate from "./Data/documentTempalteList.js";
import documentTemplate from "./Data/documentTempalteList.js";

let baseurls = baseurl[0];
const url = baseurls.url  + "/services/external/document/getTemplateSignLocation";
const SLEEP_DURATION = 5;
let apiKey = tenantLibrary[0][1];


// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 1,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': apiKey.xapikey
        }
    };


    const requestBody = JSON.stringify({

        "audit": {
            "callerId": "TestLoadTest"
        },
        "documentTemplateCode": documentTemplate[exec.vu.idInTest -1].documentTemplateCode
    });
        

 
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(response.body);

    sleep(SLEEP_DURATION);


}
