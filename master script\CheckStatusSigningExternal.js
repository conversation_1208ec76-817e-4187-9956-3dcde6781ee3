import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';

import tenant from "./Data/tenant-library.js";
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const url = baseurls.url  + "/services/external/document/checkStatusSigning";
const SLEEP_DURATION = 2;


// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 2,
            maxDuration: '10m',
        },
    },
};

// Test scenario
export default function () {
    let tenantKey = tenant[0][1];
    const paramsHeaderRequest = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': tenantKey['xapikey']
            //  ,'Cookie' : 'idjs=FDD47A1F028E0E6900095FC1D1395777'
        }
    };

 
    const requestBody = JSON.stringify({

        audit: { callerId: "UserIBAA" }
        , refNumber : ""
    });

    const response = http.post(url, requestBody, paramsHeaderRequest);
  
    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200

    });

    console.log(response.body);

}
