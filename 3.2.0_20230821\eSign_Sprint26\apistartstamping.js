import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./Data/authorization-library.js";
const URL = "https://esignhub.docsol.id:543/adimobile/esign/services/document/s/startStampingMeterai";
const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 1,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {
    let auths = auth[0][0];

    const paramsstartStampingMeterai = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'Authorization':  auths['bearerToken']
        }
    };

    const generatestartStampingMeterai = JSON.stringify({

        audit: {
            callerId: "default"
        },
        refNumber: "TEST-VIDA-04",
        tenantCode: "WOMF"

    });
    
    const checkstartStampingMeterai = http.post(URL, generatestartStampingMeterai, paramsstartStampingMeterai);

    sleep(SLEEP_DURATION);

    check(checkstartStampingMeterai, {
        'is status 200': (r) => r.status === 200
    });

    console.log([exec.vu.idInTest - 1], checkstartStampingMeterai.body);

}
