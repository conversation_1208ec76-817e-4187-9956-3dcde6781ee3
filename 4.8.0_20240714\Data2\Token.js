import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import APIS from "./Data/apis.js";
import tenant from "./Data/tenant-library.js";
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const url = baseurls.url + "/oauth/token";
const SLEEP_DURATION = 2;

export let options = {
  discardResponseBodies: false,
  scenarios: {
    contacts: {
      executor: 'per-vu-iterations',
      vus: 1,
      iterations: 20,  // Set to the total number of iterations
      maxDuration: '10m',
    },
  },
};

// Define credentials
const credentials = [
  { username: "<EMAIL>", password: "Password123!" },
  { username: "<EMAIL>", password: "Password123!" }  // Add the second set of credentials here
];

// Test scenario
export default function () {
  let tenantKey = tenant[0][1];
  const requestHeader = {
    headers: {
      'Content-Type': 'application/json',
    }
  };

  // Determine which set of credentials to use based on the current iteration
  const currentIteration = exec.vu.iterationInInstance;
  const currentCredentials = credentials[currentIteration % credentials.length];

  const formData = {
    client_id: "frontend",
    grant_type: "password",
    username: currentCredentials.username,
    password: currentCredentials.password
  };

  const response = http.post(url, formData);
  sleep(SLEEP_DURATION);

  check(response, {
    'is status 200': (r) => r.status === 200
  });

  console.log(response.body);
}
