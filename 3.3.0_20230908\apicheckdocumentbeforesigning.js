import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./Data/authorization-library.js";
import listDocument from "./Data/listdocument-library.js";
import baseurl from "./Data/baseurl.js"

let baseurls = baseurl[0]
const URL = baseurls.url  + "/services/document/s/checkDocumentBeforeSigning";

// const URL = "http://localhost:8095/services/document/s/checkDocumentBeforeSigning";
const SLEEP_DURATION = 2;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 1,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {

    let auths = auth[0][0];
    let listDoc = listDocument[0][exec.vu.idInInstance - 1];

    const paramsCheckDocumentBeforeSigning = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'Authorization': auths['bearerToken']
        }
    };

    const generateCheckDocumentBeforeSigning = JSON.stringify({

        loginId: "<EMAIL>",
        listDocumentId: [
            "00155D0B-7502-A38F-11ED-D2CA4DD7ABC0"
        ],
        audit: {
            callerId: "default"
        },
        tenantCode: "WOMF"

    });
    
    const checkCheckDocumentBeforeSigning = http.post(URL, generateCheckDocumentBeforeSigning, paramsCheckDocumentBeforeSigning);

    sleep(SLEEP_DURATION);

    check(checkCheckDocumentBeforeSigning, {
        'is status 200': (r) => r.status === 200
    });

     console.log([exec.vu.idInTest - 1], checkCheckDocumentBeforeSigning.body);

}
