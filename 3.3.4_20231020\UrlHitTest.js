import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import datauser from './Data/apis.js';
const url = "https://vfirst-handler-esignhub-dev-wicejyszur.ap-southeast-5.fcapp.run/?TO=6281363853152&MESSAGE_STATUS=1&REASON_CODE=000&DELIVERED_DATE=2023-10-27%2B12%3A18%3A44&STATUS_ERROR=8448&CLIENT_GUID=knarc183853173f410a04grui4PAITOTPXML&SUBMIT_DATE=2023-10-27%2B12%3A18%3A38&MSG_STATUS=Delivered&OPERATOR=Indonesia_Telkomsel&CIRCLE=UNKNOWN&TEXT_STATUS=Success";
const SLEEP_DURATION = 5;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 1,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {

    const headerRequest = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': 'Gy6Ka5XhgRmq'
            //  ,'Cookie' : 'idjs=FDD47A1F028E0E6900095FC1D1395777'
        }
    };

    let datausers = datauser[0][exec.vu.idInTest]

    const bodyRequest = JSON.stringify({

    
     }, null, "\t");

 
    const response = http.post(url, bodyRequest, headerRequest);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(response.body);

    sleep(SLEEP_DURATION);


}
