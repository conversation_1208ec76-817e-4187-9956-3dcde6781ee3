import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./Data/authorization-library.js";



const url = "http://localhost:8095/services/document/s/addDocumentTemplate";
const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
  discardResponseBodies: false,
  scenarios: {

      contacts: {
          executor: 'per-vu-iterations',
          vus: 1,
          iterations: 1,
          maxDuration: '1m',
      },
  },
};

// Test scenario
export default function () {

  let auths = auth[0][3];

  const ParamHeader = {
      headers: {
          'Content-Type': 'application/json'
          , 'Accept': 'application/json'
          , 'Authorization': auths['bearerToken']
      }
  };

  const BodyRequest= JSON.stringify({

documentTemplateCode: "DOC-CST2ABEECCS"+ [exec.vu.idInTest],
documentTemplateName: "Custom Doc1",
documentTemplateDescription: "Custom Doc1",
isActive: "1",
isSequence: "1",
numberOfPage: 1,
paymentSignTypeCode: "TTD",

useSignQr : "0",
tenantCode: "ADINS",
vendorCode: "VIDA",
isSignLocOnly: "0",

signer: [
  
 
  {
    signerTypeCode: "CUST",
    signTypeCode: "TTD",
    signPage: 1,
seqNo: "1",
    signLocation: {
      llx: 70.66665649414062,
      lly: 166.85334350585936,
      urx: 200.66665649414062,
      ury: 231.85334350585936,
    },
    transform: "translate3d(24px, 24px, 0px)",
    position: "{\"x\": 8.78,\"y\":8.78,\"w\":45.86,\"h\":22.93}",
    positionVida:  "{\"x\":25,\"y\":752,\"w\":130,\"h\":65}",
    positionPrivy:  "{\"x\":33,\"y\":33,\"w\":198,\"h\":106}"
  }
],

     
audit: {
          callerId: "<EMAIL>"
      },
documentExample: "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"

  });
  console.log( ParamHeader);
console.log(url);
  const checkUploadManualSignRequest = http.post(url, BodyRequest, ParamHeader);

  sleep(SLEEP_DURATION);

  check(checkUploadManualSignRequest, {
      'is status 200': (r) => r.status === 200
  });

  console.log( checkUploadManualSignRequest.body);

}