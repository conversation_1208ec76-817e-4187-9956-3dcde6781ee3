import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./Data/authorization-library.js";
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const URL = baseurls.url  + "/services/embed/document/signDocument";
const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {
    let auths = auth[0][0];
    const paramsSignDoc = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            
        }
    };

    const generateSignDoc = JSON.stringify({

        audit: {
            callerId: "default"
        },
        msg: "jJwIXY4SY5WmgDDCFMaeezsXZMLFVqawcPY2Iu6jc4mBgulnv9d7pKp9bj+jrERIJoZV1utTZQ72KhUwVpJgVTzPr11IwGFFy8odM65eJU8Xfva7Tb23AULBKaXlNF4b",
        tenantCode: "TAFS",
        documentId : "HcsorMLJzu8v/sbZ5aePnlL0nqavn3Sa1FJPjSLejwMiUXfDKU+lyajqB6DezIlo",
        
    });
    
    const checkSignDoc = http.post(URL, generateSignDoc, paramsSignDoc);

    sleep(SLEEP_DURATION);

    check(checkSignDoc, {
        'is status 200': (r) => r.status === 200
    });

    console.log([exec.vu.idInTest - 1], checkSignDoc.body);

}
