import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import userManagementData from "./Data/userManagementData.js";
import baseurl from "./Data/baseurl.js";
import authorization from "./Data/authorization-library.js";

// Configuration
let baseurls = baseurl[0];
const url = baseurls.url + "/services/user-management/s/insertUserManagement";
const healthCheckUrl = baseurls.url + "/health"; // Health check endpoint
const SLEEP_DURATION = 10.0; // Very long sleep - 10 seconds

// Use <EMAIL> for authorization (index 3)
const authData = authorization[3];

// Continue from index 167500 - remaining users: 832500
const START_INDEX = 167500;
const REMAINING_USERS = 1000000 - START_INDEX; // 832500 users remaining

// Ultra conservative configuration - minimal load
export let options = {
    discardResponseBodies: false,
    scenarios: {
        insert_users_ultra_conservative: {
            executor: 'shared-iterations',
            vus: 5,             // Minimal VUs - only 5
            iterations: REMAINING_USERS, // 832500 remaining iterations
            maxDuration: '600m', // 10 hours max duration
        },
    },
    thresholds: {
        http_req_duration: ['p(95)<30000'], // Very lenient - 30s
        http_req_failed: ['rate<0.5'],      // Allow 50% failure rate initially
    },
};

// Global variables for health monitoring
let consecutiveFailures = 0;
let lastSuccessTime = Date.now();

// Health check function
function checkServerHealth() {
    console.log("Performing server health check...");
    
    try {
        const healthResponse = http.get(healthCheckUrl, {
            timeout: '10s',
            headers: {
                'Accept': 'application/json'
            }
        });
        
        if (healthResponse.status === 200) {
            console.log("✓ Server health check passed");
            return true;
        } else {
            console.log(`⚠ Server health check failed: Status ${healthResponse.status}`);
            return false;
        }
    } catch (e) {
        console.log(`⚠ Server health check failed: ${e.message}`);
        return false;
    }
}

// Test scenario
export default function () {
    // Use scenario iteration counter + start index
    const globalIndex = START_INDEX + exec.scenario.iterationInInstance;
    
    // Safety check: ensure index is within bounds
    if (globalIndex >= userManagementData.totalUsers) {
        console.error(`Index ${globalIndex} exceeds available data (${userManagementData.totalUsers}). Skipping.`);
        return;
    }
    
    // Get user data by global index
    const userData = userManagementData.getUserByIndex(globalIndex);
    
    if (!userData) {
        console.error(`No user data found for index ${globalIndex}`);
        return;
    }

    // Check if we should pause due to consecutive failures
    if (consecutiveFailures >= 10) {
        console.log(`⚠ Too many consecutive failures (${consecutiveFailures}). Pausing for server recovery...`);
        sleep(60); // Pause for 1 minute
        
        // Perform health check
        if (!checkServerHealth()) {
            console.log("Server still unhealthy. Extending pause...");
            sleep(120); // Additional 2 minutes
        }
        
        consecutiveFailures = 0; // Reset counter
    }

    // Request headers
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': authData.bearerToken
        },
        timeout: '30s' // 30 second timeout
    };

    // Request body with correct configuration
    const requestBody = JSON.stringify({
        "audit": {
            "callerId": "<EMAIL>"
        },
        "fullName": userData.fullName,
        "tenantCode": "ADINS",
        "loginId": userData.loginId,
        "roleCode": "USER_EDITOR",
        "password": "ADINS",
        "officeCode": "0191"
    });

    // Add long delay to avoid overwhelming the server
    sleep(SLEEP_DURATION);

    // Make the request with very aggressive retry logic
    let response;
    let retryCount = 0;
    const maxRetries = 7; // Even more retries

    do {
        try {
            response = http.post(url, requestBody, requestHeader);
        } catch (e) {
            console.log(`Request failed with exception: ${e.message}`);
            response = { status: 0, body: "" };
        }
        
        // If successful, break out of retry loop
        if (response.status === 200) {
            try {
                const body = JSON.parse(response.body);
                if (body.status && body.status.code === 0) {
                    consecutiveFailures = 0; // Reset failure counter
                    lastSuccessTime = Date.now();
                    break; // Success
                }
            } catch (e) {
                // Continue to retry if can't parse response
            }
        }
        
        // If it's a client error (4xx), don't retry (except 429)
        if (response.status >= 400 && response.status < 500 && response.status !== 429) {
            break;
        }
        
        // For server errors, connection issues, or rate limiting, wait and retry
        if (retryCount < maxRetries && (response.status >= 500 || response.status === 429 || response.status === 0)) {
            retryCount++;
            consecutiveFailures++;
            
            // Very long exponential backoff - up to 10 minutes
            const baseBackoff = Math.pow(2, retryCount) * 10; // 20s, 40s, 80s, 160s, 320s, 640s
            const jitter = Math.random() * 30; // 0-30s random
            const backoffTime = Math.min(600, baseBackoff + jitter); // Max 10 minutes
            
            console.log(`Retry ${retryCount}/${maxRetries} for user ${globalIndex + 1} after ${backoffTime.toFixed(1)}s (Status: ${response.status})`);
            
            // Perform health check on longer retries
            if (retryCount >= 3) {
                console.log("Performing health check before retry...");
                if (!checkServerHealth()) {
                    console.log("Server unhealthy. Extending backoff time...");
                    sleep(Math.min(300, backoffTime * 2)); // Double the wait time, max 5 minutes
                }
            }
            
            sleep(backoffTime);
        }
        
    } while (retryCount < maxRetries && (response.status >= 500 || response.status === 429 || response.status === 0));

    // Check response
    const isSuccess = check(response, {
        'is status 200': (r) => r.status === 200,
        'response has success status': (r) => {
            try {
                const body = JSON.parse(r.body);
                return body.status && body.status.code === 0;
            } catch (e) {
                return false;
            }
        }
    });

    // Log progress every 100 requests (less frequent)
    if ((globalIndex - START_INDEX) % 100 === 0) {
        const progress = ((globalIndex - START_INDEX + 1) / REMAINING_USERS * 100).toFixed(2);
        const timeSinceLastSuccess = (Date.now() - lastSuccessTime) / 1000 / 60; // minutes
        console.log(`Progress: User ${globalIndex + 1} (${progress}% of remaining) - LoginId: ${userData.loginId}, Status: ${response.status}`);
        console.log(`Last success: ${timeSinceLastSuccess.toFixed(1)} minutes ago, Consecutive failures: ${consecutiveFailures}`);
    }

    // Log errors (but less verbose for server errors)
    if (!isSuccess) {
        if (response.status >= 500 || response.status === 0) {
            // Server errors - log less frequently
            if ((globalIndex - START_INDEX) % 50 === 0) {
                console.error(`Server error for user ${globalIndex + 1}: Status ${response.status}`);
            }
        } else {
            // Client errors - always log these
            console.error(`Failed to insert user ${globalIndex + 1} (${userData.loginId}): Status ${response.status}`);
            
            try {
                const errorBody = JSON.parse(response.body);
                if (errorBody.status && errorBody.status.message) {
                    console.log(`Error: ${errorBody.status.message}`);
                }
            } catch (e) {
                // Ignore JSON parse errors
            }
        }
    }
}

// Setup function
export function setup() {
    console.log("=== InsertUserManagement Ultra Conservative Load Test Setup ===");
    console.log(`Target URL: ${url}`);
    console.log(`Starting from index: ${START_INDEX}`);
    console.log(`Remaining users to insert: ${REMAINING_USERS}`);
    console.log(`Total users available: ${userManagementData.totalUsers}`);
    console.log(`Authorization: ${authData.username}`);
    console.log(`VUs: 5 (ultra conservative)`);
    console.log(`Sleep duration: ${SLEEP_DURATION}s (ultra conservative)`);
    console.log(`Max retries: 7 (very aggressive retry)`);
    console.log(`Health monitoring: Enabled`);
    console.log(`Auto-pause: Enabled (after 10 consecutive failures)`);
    console.log(`Total iterations: ${REMAINING_USERS}`);
    console.log(`Estimated duration: ${(REMAINING_USERS * SLEEP_DURATION / 5 / 60).toFixed(0)} minutes`);
    
    // Perform initial health check
    console.log("\nPerforming initial server health check...");
    if (checkServerHealth()) {
        console.log("✓ Server appears healthy. Proceeding with load test.");
    } else {
        console.log("⚠ Server appears unhealthy. Proceeding with caution.");
        console.log("The script will automatically pause and retry if server issues persist.");
    }
    
    // Validate data
    if (userManagementData.totalUsers < 1000000) {
        throw new Error(`Insufficient user data. Need 1000000 users, but only have ${userManagementData.totalUsers}`);
    }
    
    const sampleUser = userManagementData.getUserByIndex(START_INDEX);
    if (!sampleUser) {
        throw new Error(`No sample user data available at index ${START_INDEX}`);
    }
    
    console.log(`Sample user: Index=${START_INDEX}, LoginId=${sampleUser.loginId}, FullName=${sampleUser.fullName}`);
    console.log("Setup complete. Starting ultra conservative load test...");
    
    return {};
}

// Teardown function
export function teardown(data) {
    console.log("=== InsertUserManagement Ultra Conservative Load Test Complete ===");
    console.log(`Processed users from index ${START_INDEX} onwards`);
    console.log("Check the K6 summary for detailed metrics.");
    console.log("If server errors persisted, consider waiting longer before retry or contacting system administrator.");
}
