import psycopg2
from psycopg2 import sql
from dotenv import load_dotenv
import os
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import base64
import re
import execjs
import json
import hashlib

load_dotenv()

def getIdSigningAuditTrail(phone):

    db_config = {
        'dbname': os.getenv('dataSourceName'),
        'user': os.getenv('dataSourceUsername'),
        'password': os.getenv('dataSourcePassword'),
        'host': os.getenv('dataSourceUrl'),
        'port': os.getenv('dataSourcePort')
    }

    try:
        connection = psycopg2.connect(**db_config)
        cursor = connection.cursor()

        query = '''
        SELECT tspat.id_signing_process_audit_trail 
        FROM tr_signing_process_audit_trail tspat
        JOIN tr_signing_process_audit_trail_detail tspatd 
            ON tspat.id_signing_process_audit_trail = tspatd.id_signing_process_audit_trail
        WHERE tspat.hashed_phone_no = %s order by id_signing_process_audit_trail desc limit 1
        '''

        hashedPhone = hashlib.sha256(phone.encode()).hexdigest()

        cursor.execute(query, (hashedPhone, ))

        result = cursor.fetchone()

        if result is None:
            IdSigningAuditTrail = ""
        else : 
            IdSigningAuditTrail=  result[0]
        
        connection.commit()

    except (Exception, psycopg2.DatabaseError) as error:
        print(f"Error occurred: {error}")
    finally:
        if connection:
            cursor.close()
            connection.close()

    return IdSigningAuditTrail

def updateUserIdSigningAuditTrail():
    with open('data/user.js', 'r') as file:
        user = file.read()

    context = execjs.compile(user)
    users = context.eval('user')

    for user in users:
        phone = user['nohp']
        
        idSigningAuditTrail = getIdSigningAuditTrail(phone)
        print(idSigningAuditTrail)

        if idSigningAuditTrail:
            user['idSigningProcessAuditTrailDetail'] = idSigningAuditTrail

    with open('data/user.js', 'w') as file:
        file.write("const user = \n")
        file.write("    [\n")
        
        for user in users:
            file.write("        {\n")
            file.write(f"            nik: '{user['nik']}',\n")
            file.write(f"            date: '{user['date']}',\n")
            file.write(f"            username: '{user['username']}',\n")
            file.write(f"            nohp: '{user['nohp']}',\n")
            file.write(f"            msg: '{user['msg']}',\n")
            file.write(f"            otp: '{user['otp']}',\n")
            file.write(f"            documentId: '{user['documentId']}',\n")
            file.write(f"            refNumber: '{user['refNumber']}',\n")
            file.write(f"            refNumber2: '{user['refNumber']}',\n")
            file.write(f"            idSigningProcessAuditTrailDetail: '{user['idSigningProcessAuditTrailDetail']}',\n")
            file.write(f"            bearerToken: '{user['bearerToken']}'\n")
            file.write("        },\n")
        
        file.write("    ];\n")
        file.write("module.exports = user;\n")

# updateUserIdSigningAuditTrail()