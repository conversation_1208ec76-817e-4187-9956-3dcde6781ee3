import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import tenant from "./Data/tenant-library.js";
import baseurl from "./Data/baseurl.js";
import APIS from "./Data/apis.js";


let baseurls = baseurl[0];
const url = baseurls.url + "/services/external/user/generateInvLink";
const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {
    let tenantKey = tenant[1];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': tenantKey.xapikey
        }
    };

    
    let apis = APIS[0][exec.vu.idInTest-1];
    
    if (apis == null) {
        apis = {
        "username": ""
        , "nik": ""
        , "nohp": ""
        };
    }

    const requestBody = JSON.stringify({

        audit: { callerId: apis.username },
        alamat : "ANCOL SELATAN",
        email: [apis.username] + "<EMAIL>",
        idKtp: apis.nik,
        jenisKelamin : "M",
        kecamatan : "Tanjung Priok",
        kelurahan : "Sunter Agung",
        kodePos : "14350",
        kota : "JAKARTA UTARA",
        nama : "UserACJI",
        provinsi : "DKI JAKARTA",
        tmpLahir: "BOGOR",
        tlp: apis.nohp,
        tglLahir: apis.date,
        type : "EMPLOYEE"
    });
    
    const response = http.post(url, requestBody, requestHeader);

    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200
    });

    console.log([exec.vu.idInTest - 1], response.body);

}
