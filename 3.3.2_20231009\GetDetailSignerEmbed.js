import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';

import auth from "./Data/authorization-library.js";
import baseurl from "./Data/baseurl.js"

let baseurls = baseurl[0]
const url = baseurls.url  + "/services/embed/user/getSignerDetailEmbed";
const SLEEP_DURATION = 2;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 2,
            iterations: 1,
            maxDuration: '10m',
        },
    },
};

// Test scenario
export default function () {
    let auths = auth[0][0];
    const requestHeaeder = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'

        }
    };


    // Get User Profile (user/s/profiles)
    const requestBody = JSON.stringify({

        audit: { callerId: "<EMAIL>" }
        , msg: "jJwIXY4SY5WmgDDCFMaeezsXZMLFVqawcPY2Iu6jc4mBgulnv9d7pKp9bj%2BjrERIJoZV1utTZQ72KhUwVpJgVTzPr11IwGFFy8odM65eJU%2FckrUxz9XExUlmEC%2FVSkVv",
        tenantCode: "ADINS",
        vendorCode : "VIDA"

    });
    const apiResponse = http.post(url, requestBody, requestHeaeder);
  
    sleep(SLEEP_DURATION);

    check(apiResponse, {
        'is status 200': (r) => r.status === 200

    });

    console.log(apiResponse.body);

}
