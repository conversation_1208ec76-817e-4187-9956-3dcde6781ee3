import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./Data/authorization-library.js";
import listDocument from "./Data/listdocument-library.js";
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const URL = baseurls.url  + "/services/embed/document/checkDocumentBeforeSigningEmbed";

const SLEEP_DURATION = 2;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 1,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {

    let auths = auth[0][0];
    let listDoc = listDocument[0][exec.vu.idInInstance - 1];

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
          
        }
    };

    const requestBody = JSON.stringify({

        msg: "Pvkay+O8L4Wg/1CNGMLhW21BjlToc9IcZxoHcrGf/miEt8B4Hz8toY4TXGChGu/6VgRrvRAipZly9SV8a+oPoLjLkWnTYssA+lnzKtaHYVrjbsFWCnfnBzC4q2NwDaSb",
        listDocumentId: [
            "CN48HtkuYOR7kyE4BdAo2LUJZ4bF7uRyOt22M/W2MNOU6b2M1gC+0tUGvbPHHrDb"
        ],
        audit: {
            callerId: "default"
        },
        tenantCode: "WOMF",
        vendorCode : "PRIVY"


    });
    
    const response = http.post(URL, requestBody, requestHeader);

    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200
    });

     console.log([exec.vu.idInTest - 1], response.body);

}
