import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./authorization-library.js";
import listDocument from "./listdocument-library.js";

const URL = "http://localhost:8095/services/document/s/signConfirmDokumen";
const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 10,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {

    let auths = auth[0][0];
    let listDoc = listDocument[0][exec.vu.idInInstance - 1];

    const paramsSignConfirmDokumen = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'Authorization': auths['bearerToken']
        }
    };

    const generateSignConfirmDokumen = JSON.stringify({

        audit: {
            callerId: "default"
        },
        email: "<EMAIL>",
        ipAddress: "************",
        browser: "mozila",
        documentId: [
            listDoc.documentId
        ]

    });
    
    const checkSignConfirmDokumen = http.post(URL, generateSignConfirmDokumen, paramsSignConfirmDokumen);

    sleep(SLEEP_DURATION);

    check(checkSignConfirmDokumen, {
        'is status 200': (r) => r.status === 200
    });

    console.log([exec.vu.idInTest - 1], checkSignConfirmDokumen.body);

}
