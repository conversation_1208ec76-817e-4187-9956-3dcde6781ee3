import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';

import baseurl from "./Data/baseurl.js"
import tenant from "./Data/tenant-library.js"


let baseurls = baseurl[0]
const url = baseurls + "/services/user/sentOtpActivationUser";

const SLEEP_DURATION = 5;
const USERNAME = "<EMAIL>";
const PASSWORD = "supersupeR4!";
const USERNAME_2 = "<EMAIL>";


// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {
    let tenantKey = tenant[0][1];
    const paramSendOtpSigning = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': tenantKey['xapikey']
            //  ,'Cookie' : 'idjs=FDD47A1F028E0E6900095FC1D1395777'
        }
    };

  




    const sendotprequest = JSON.stringify({

        audit: { callerId:"MALVIN" },
        phoneNo: "000101809001",
        msg : ""
    }, null, "\t");

    sleep(SLEEP_DURATION);
    const sendOtpResponse = http.post(url, sendotprequest, paramSendOtpSigning);
    check(sendOtpResponse, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(sendOtpResponse.body);




}
