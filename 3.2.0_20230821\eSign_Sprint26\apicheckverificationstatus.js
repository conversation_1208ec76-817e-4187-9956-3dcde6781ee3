import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./Data/authorization-library.js";
import listDocument from "./Data/listdocument-library.js";

const URL = "https://esignhub.docsol.id:543/adimobile/esign/services/external/user/checkVerificationStatus";

// const URL = "http://localhost:8095/services/document/s/checkDocumentBeforeSigning";
const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {

    let auths = auth[0][0];
    let listDoc = listDocument[0][exec.vu.idInInstance - 1];

    const paramsCheckVerificationStatus = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': 'Gy6Ka5XhgRmq@ADINS'
        }
    };

    const generateCheckVerificationStatus = JSON.stringify({

        
        audit: {
            callerId: "default"
        },
        trxNo: "13397"

    });
    
    const CheckCheckVerificationStatus = http.post(URL, generateCheckVerificationStatus, paramsCheckVerificationStatus);

    sleep(SLEEP_DURATION);

    check(CheckCheckVerificationStatus, {
        'is status 200': (r) => r.status === 200
    });

    console.log( CheckCheckVerificationStatus.body);

}
