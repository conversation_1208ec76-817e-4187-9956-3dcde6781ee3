/*
    Setiap mau run yang membutuhkan token, run ini API dahulu

    Url: {http://gdkwebsvr:7021/adimobile/esign/oauth/token}
*/

const authorization = [
    [
        {
            // 0
            username      : '<EMAIL>',
            password      : 'Password123!',
            bearerToken   : 'Bearer ' + 'U/vg0O+cwse768io/0srmRewdho=',
            msg           : 'Pvkay+O8L4Wg/1CNGMLhW/S+yC6Ea0VnV5HuBhiqZ58OLQ34hshCPd3NbGINegR45hOdKC3Q8vWlMS0VGQnS1Q=='
        },
        {
            // 1
            username      : '<PERSON><PERSON><PERSON><PERSON><PERSON>@ADINS',
            password      : 'password',
            bearerToken   : 'Bearer ' + 'LtWc1KVifr+aSVC8kyFJCWLd6d4='
        },
        {
            // 2
            username      : '<EMAIL>',
            password      : 'password',
            bearerToken   : 'Bearer ' + 'nhOwLOEWu9+XUs7vBrzfKfV1bBk='
        },
        {
            // 3
            username      : '<EMAIL>',
            password      : 'Password123!',
            bearerToken   : 'Bearer ' + '5HXvnwlsZkfsRaHeyv+5CvmVaWg='
        }
    ]
];
module.exports = authorization;
