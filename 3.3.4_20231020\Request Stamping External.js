import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import tenant from "./Data/tenant-library.js";
const URL = "http://localhost:8095/services/external/document/requestStamping";
const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 1,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {
    let tnt = tenant[0][0];
    const paramsstartStampingMeterai = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': tnt['xapikey']
        }
    };

    const generatestartStampingMeterai = JSON.stringify({

        audit: {
            callerId: "default"
        },
        refNumber: "EXTSIGN-PRIVY-WOMF-0009",


    });
    
    const checkstartStampingMeterai = http.post(URL, generatestartStampingMeterai, paramsstartStampingMeterai);

    sleep(SLEEP_DURATION);

    check(checkstartStampingMeterai, {
        'is status 200': (r) => r.status === 200
    });

    console.log([exec.vu.idInTest - 1], checkstartStampingMeterai.body);

}
