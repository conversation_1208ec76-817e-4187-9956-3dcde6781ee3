import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import auth from "./Data/authorization-library.js"
import baseurl from "./Data/baseurl.js";
import tenant from "./Data/tenant-library.js";
import docId from "./Data/DocumentId.js";


let baseurls = baseurl[0];
const url = baseurls.url + "/services/embed/document/signConfirmDocument";
let authData = auth[1];


const SLEEP_DURATION = 5;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {
    let tenantKey = tenant[1];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            ,'Authorization': authData.bearerToken


        }
    };

  
    const requestBody = JSON.stringify({
        audit:{
            callerId:"zV8OBa9gryd8t6t/IX/eaTLD7GgrjdMSbniIxxVbjqynkPiCMtf7mI8Al7/j6ayQLb8HsbguU3IuivF2J4LG8QOu9yNrcKxNaDiRDCJE3nLNonjwFvQ8gW3fYR+OCeSLWtg+kNDbUphLKXNglyfKrw=="
        },
        msg:"zV8OBa9gryd8t6t/IX/eaTLD7GgrjdMSbniIxxVbjqynkPiCMtf7mI8Al7/j6ayQLb8HsbguU3IuivF2J4LG8QOu9yNrcKxNaDiRDCJE3nLNonjwFvQ8gW3fYR+OCeSLWtg+kNDbUphLKXNglyfKrw==",
        browser: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        ipAddress : "**************",
        documentIds : [docId[exec.vu.idInInstance-1].docId],
        tenantCode : "ADINS"

    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(response.body);




}
