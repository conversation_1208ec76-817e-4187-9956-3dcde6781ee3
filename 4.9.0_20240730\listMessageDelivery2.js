import http from "k6/http";
import { check, sleep } from "k6";
import baseurl from "./Data/baseurl.js";
import auth from "./Data/authorization-library.js"

let baseurls = baseurl[0];
const url =  baseurls.url  + "/services/messageDelivery/s/listMessageDelivery";
const SLEEP_DURATION = 0;
let authData = auth[1];

export let options = {
    discardResponseBodies: false,
    scenarios: {
        listMessageDelivery: {
            executor: 'per-vu-iterations',
            vus: 10,
            iterations: 10000000000,
            maxDuration: '10m',
        },
    },
};

export default function () {
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            ,'Authorization': authData.bearerToken
        }
    };

    const requestBody = JSON.stringify({
        page: 1,
        vendorCode: "",
        messageMedia: "",
        reportTimeStart: "2024-07-01",
        reportTimeEnd: "2024-07-08",
        requestTimeStart: "",
        requestTimeEnd: "",
        deliveryStatus: "",
        recipient: "",
        audit: { callerId: "<EMAIL>" },
        tenantCode: "ADINS"
    });

    const response = http.post(url, requestBody, requestHeader);

    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200
    });

    console.log(response.body);
}
