import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import userManagementData from "./Data/userManagementData.js";
import baseurl from "./Data/baseurl.js";
import authorization from "./Data/authorization-library.js";

// Configuration
let baseurls = baseurl[0];
const url = baseurls.url + "/services/user-management/s/insertUserManagement";
const SLEEP_DURATION = 0.5; // Slightly longer sleep for batch processing

// Use <EMAIL> for authorization (index 3)
const authData = authorization[3];

// Safe configuration - exactly 1 million users, no more
export let options = {
    discardResponseBodies: false,
    scenarios: {
        insert_users_safe: {
            executor: 'shared-iterations',
            vus: 50,            // 50 virtual users
            iterations: 1000000, // Exactly 1 million iterations total
            maxDuration: '180m', // 3 hours max duration
        },
    },
    thresholds: {
        http_req_duration: ['p(95)<10000'], // 95% of requests should be below 10s
        http_req_failed: ['rate<0.05'],     // Error rate should be below 5%
    },
};

// Test scenario
export default function () {
    // Use scenario iteration counter directly - this ensures sequential access
    const globalIndex = exec.scenario.iterationInInstance;
    
    // Safety check: ensure index is within bounds
    if (globalIndex >= userManagementData.totalUsers) {
        console.error(`Index ${globalIndex} exceeds available data (${userManagementData.totalUsers}). Skipping.`);
        return;
    }
    
    // Get user data by global index
    const userData = userManagementData.getUserByIndex(globalIndex);
    
    if (!userData) {
        console.error(`No user data found for index ${globalIndex}`);
        return;
    }

    // Request headers
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': authData.bearerToken
        }
    };

    // Request body with correct configuration
    const requestBody = JSON.stringify({
        "audit": {
            "callerId": "<EMAIL>"
        },
        "fullName": userData.fullName,
        "tenantCode": "ADINS",
        "loginId": userData.loginId,
        "roleCode": "USER_EDITOR",
        "password": "ADINS",
        "officeCode": "0191"
    });

    // Add delay to avoid overwhelming the server
    sleep(SLEEP_DURATION);

    // Make the request with retry logic
    let response;
    let retryCount = 0;
    const maxRetries = 3;

    do {
        response = http.post(url, requestBody, requestHeader);
        
        // If successful, break out of retry loop
        if (response.status === 200) {
            break;
        }
        
        // If it's a client error (4xx), don't retry
        if (response.status >= 400 && response.status < 500 && response.status !== 429) {
            break;
        }
        
        // For server errors or rate limiting, wait and retry
        if (retryCount < maxRetries && (response.status >= 500 || response.status === 429)) {
            retryCount++;
            const backoffTime = Math.min(60, Math.pow(2, retryCount) + Math.random() * 5);
            console.log(`Retry ${retryCount}/${maxRetries} for user ${globalIndex + 1} after ${backoffTime.toFixed(1)}s`);
            sleep(backoffTime);
        }
        
    } while (retryCount < maxRetries && (response.status >= 500 || response.status === 429));

    // Check response
    const isSuccess = check(response, {
        'is status 200': (r) => r.status === 200,
        'response has success status': (r) => {
            try {
                const body = JSON.parse(r.body);
                return body.status && body.status.code === 0;
            } catch (e) {
                return false;
            }
        }
    });

    // Log progress every 1000 requests
    if (globalIndex % 1000 === 0) {
        console.log(`Progress: User ${globalIndex + 1}/${userManagementData.totalUsers} (${((globalIndex + 1) / userManagementData.totalUsers * 100).toFixed(2)}%) - LoginId: ${userData.loginId}, Status: ${response.status}`);
    }

    // Log errors for debugging
    if (!isSuccess) {
        console.error(`Failed to insert user ${globalIndex + 1} (${userData.loginId}): Status ${response.status}`);
        
        // Try to parse error message
        try {
            const errorBody = JSON.parse(response.body);
            if (errorBody.status && errorBody.status.message) {
                console.log(`Error: ${errorBody.status.message}`);
            }
        } catch (e) {
            // Ignore JSON parse errors
        }
    }
}

// Setup function to validate data availability
export function setup() {
    console.log("=== InsertUserManagement Safe Load Test Setup ===");
    console.log(`Target URL: ${url}`);
    console.log(`Total users to insert: ${userManagementData.totalUsers}`);
    console.log(`Authorization: ${authData.username}`);
    console.log(`VUs: 50`);
    console.log(`Total iterations: 1000000`);
    console.log(`Executor: shared-iterations (safe mode)`);
    console.log(`Estimated duration: ${(1000000 * SLEEP_DURATION / 50 / 60).toFixed(0)} minutes`);
    
    // Validate that we have enough user data
    if (userManagementData.totalUsers < 1000000) {
        throw new Error(`Insufficient user data. Need 1000000 users, but only have ${userManagementData.totalUsers}`);
    }
    
    // Test a sample request to validate configuration
    console.log("Testing sample request...");
    const sampleUser = userManagementData.getUserByIndex(0);
    if (!sampleUser) {
        throw new Error("No sample user data available");
    }
    
    console.log(`Sample user: LoginId=${sampleUser.loginId}, FullName=${sampleUser.fullName}`);
    console.log("Setup complete. Starting safe load test...");
    return {};
}

// Teardown function for final reporting
export function teardown(data) {
    console.log("=== InsertUserManagement Safe Load Test Complete ===");
    console.log("Check the K6 summary for detailed metrics.");
    console.log("Review logs for any failed insertions that may need manual retry.");
}
