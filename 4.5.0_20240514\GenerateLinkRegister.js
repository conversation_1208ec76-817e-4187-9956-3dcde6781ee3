import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import tenant from "./Data/tenant-library.js";
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const url = baseurls.url  + "/services/user/generateInvitationLinkV2";
const SLEEP_DURATION = 2;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 2,
            iterations: 1,
            maxDuration: '10m',
        },
    },
};

// Test scenario
export default function () {

    let tnt = tenant[0][0];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': tnt['xapikey']
        }
    };


    // Get User Profile (user/s/profiles)
    const requestBody = JSON.stringify({
        audit: { callerId: apis.username }
        , tenantCode: "ADINS"
        , users: [{
          nama: apis.username,
          tlp: apis.nohp  + [exec.vu.idInTest],
          email: "MALVINTEST004" + [exec.vu.idInTest ] + "@esignhub.my.id",
          jenisKelamin: "M",
          tmpLahir: "BOGOR",
          tglLahir: "1980-01-01",
          idKtp: apis.nik,
          provinsi: "Jawa Barat",
          kota: "Bogor",
          kecamatan: "Bogor Selatan",
          kelurahan: "Baranangsiang",
          kodePos: "16143",
          alamat: "JL. SAWOKNA NO.1000 BANTAR KEMANG"
          , vendorCode: "PRIVY"
        }]

    });
    const response = http.post(url, requestBody, requestHeader);
  
    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200

    });

    console.log(response.body);

}
