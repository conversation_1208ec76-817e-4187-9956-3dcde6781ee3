import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import users from "../data/user.js";
import tenants from "../data/apiKey.js";
import baseurl from "../data/baseurl.js";

let baseurls = baseurl[0];
const url = baseurls.url  + "/services/external/user/checkRegistration";
const SLEEP_DURATION = 5;
let tenant = tenants[1];



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': tenant.xapikey
        }
    };

    let user = users[exec.vu.idInTest - 1];
 
    if (user == null) {
      user = {
        "username": ""
        , "nik": ""
        , "nohp": ""
      };
    }

    const requestBody = JSON.stringify({

        audit: { callerId:"MALVIN" },
        audit: { callerId: "UserIBAA" }
        , dataType: "NIK"
        , userData: user.nik
        
    }, null, "\t");

 
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(response.body);

    sleep(SLEEP_DURATION);


}
