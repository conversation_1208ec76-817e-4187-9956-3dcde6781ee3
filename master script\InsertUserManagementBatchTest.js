import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import userManagementData from "./Data/userManagementData.js";
import baseurl from "./Data/baseurl.js";
import authorization from "./Data/authorization-library.js";

// Configuration
let baseurls = baseurl[0];
const url = baseurls.url + "/services/user-management/s/insertUserManagement";
const SLEEP_DURATION = 0.5;

// Use <EMAIL> for authorization (index 3)
const authData = authorization[3];

// Small test configuration - only 20 users for testing batch script
export let options = {
    discardResponseBodies: false,
    scenarios: {
        insert_users_batch_test: {
            executor: 'per-vu-iterations',
            vus: 2,            // 2 virtual users
            iterations: 10,    // 10 iterations per VU = 20 total
            maxDuration: '5m', // 5 minutes max duration
        },
    },
    thresholds: {
        http_req_duration: ['p(95)<10000'], // 95% of requests should be below 10s
        http_req_failed: ['rate<0.2'],      // Error rate should be below 20% for testing
    },
};

// Test scenario - using batch script logic
export default function () {
    // Calculate global user index - start from index 100 to avoid duplicates
    const globalIndex = 100 + (exec.vu.idInTest - 1) * 10 + exec.scenario.iterationInTest;
    
    // Get user data by global index
    const userData = userManagementData.getUserByIndex(globalIndex);
    
    if (!userData) {
        console.error(`No user data found for index ${globalIndex}`);
        return;
    }

    // Request headers
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': authData.bearerToken
        }
    };

    // Request body with correct configuration
    const requestBody = JSON.stringify({
        "audit": {
            "callerId": "<EMAIL>"
        },
        "fullName": userData.fullName,
        "tenantCode": "ADINS",
        "loginId": userData.loginId,
        "roleCode": "USER_EDITOR",
        "password": "ADINS",
        "officeCode": "0191"
    });

    // Add delay to avoid overwhelming the server
    sleep(SLEEP_DURATION);

    // Make the request with retry logic
    let response;
    let retryCount = 0;
    const maxRetries = 2;

    do {
        response = http.post(url, requestBody, requestHeader);
        
        // If successful, break out of retry loop
        if (response.status === 200) {
            break;
        }
        
        // If it's a client error (4xx), don't retry
        if (response.status >= 400 && response.status < 500 && response.status !== 429) {
            break;
        }
        
        // For server errors or rate limiting, wait and retry
        if (retryCount < maxRetries && (response.status >= 500 || response.status === 429)) {
            retryCount++;
            const backoffTime = Math.min(10, Math.pow(2, retryCount));
            console.log(`Retry ${retryCount}/${maxRetries} for user ${globalIndex + 1} after ${backoffTime}s`);
            sleep(backoffTime);
        }
        
    } while (retryCount < maxRetries && (response.status >= 500 || response.status === 429));

    // Check response
    const isSuccess = check(response, {
        'is status 200': (r) => r.status === 200,
        'response has success status': (r) => {
            try {
                const body = JSON.parse(r.body);
                return body.status && body.status.code === 0;
            } catch (e) {
                return false;
            }
        }
    });

    // Log progress every 5 requests
    if (globalIndex % 5 === 0) {
        console.log(`Progress: User ${globalIndex + 1} - LoginId: ${userData.loginId}, Status: ${response.status}`);
    }

    // Log results
    if (isSuccess) {
        console.log(`✓ Successfully inserted user ${globalIndex + 1} (${userData.loginId})`);
    } else {
        console.error(`✗ Failed to insert user ${globalIndex + 1} (${userData.loginId}): Status ${response.status}`);
        
        // Try to parse error message
        try {
            const errorBody = JSON.parse(response.body);
            if (errorBody.status && errorBody.status.message) {
                console.log(`Error: ${errorBody.status.message}`);
            }
        } catch (e) {
            // Ignore JSON parse errors
        }
    }
}

// Setup function
export function setup() {
    console.log("=== InsertUserManagement Batch Test Setup ===");
    console.log(`Target URL: ${url}`);
    console.log(`Total available users: ${userManagementData.totalUsers}`);
    console.log(`Authorization: ${authData.username}`);
    console.log(`VUs: 2`);
    console.log(`Iterations per VU: 10`);
    console.log(`Total test requests: 20`);
    console.log(`Starting from user index: 100 (to avoid duplicates)`);
    
    // Test data access
    console.log("\nTesting data access...");
    for (let i = 100; i < 103; i++) {
        const testUser = userManagementData.getUserByIndex(i);
        if (testUser) {
            console.log(`  User ${i}: LoginId=${testUser.loginId}, FullName=${testUser.fullName}`);
        } else {
            console.error(`  User ${i}: No data found`);
        }
    }
    
    console.log("Setup complete. Starting batch test...");
    return {};
}

// Teardown function
export function teardown(data) {
    console.log("=== InsertUserManagement Batch Test Complete ===");
    console.log("This was a small batch test. Check results before running full load test.");
}
