import psycopg2
from psycopg2 import sql
from dotenv import load_dotenv
import os
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import base64
import re
import execjs
import json
import hashlib

load_dotenv()

def getOtpCode(NIK):

    db_config = {
        'dbname': os.getenv('dataSourceName'),
        'user': os.getenv('dataSourceUsername'),
        'password': os.getenv('dataSourcePassword'),
        'host': os.getenv('dataSourceUrl'),
        'port': os.getenv('dataSourcePort')
    }

    try:
        connection = psycopg2.connect(**db_config)
        cursor = connection.cursor()

        query = '''
        select otp_code from am_msuser amu 
        where amu.hashed_id_no = %s 
        '''

        hashedNIK = hashlib.sha256(NIK.encode()).hexdigest()

        cursor.execute(query, (hashedNIK, ))

        result = cursor.fetchone()

        if result is None:
            otpCode = ""
        else : 
            otpCode=  result[0]
        
        connection.commit()

    except (Exception, psycopg2.DatabaseError) as error:
        print(f"Error occurred: {error}")
    finally:
        if connection:
            cursor.close()
            connection.close()

    return otpCode

def getResetCode(NIK):
    
    db_config = {
        'dbname': os.getenv('dataSourceName'),
        'user': os.getenv('dataSourceUsername'),
        'password': os.getenv('dataSourcePassword'),
        'host': os.getenv('dataSourceUrl'),
        'port': os.getenv('dataSourcePort')
    }

    try:
        connection = psycopg2.connect(**db_config)
        cursor = connection.cursor()

        query = '''
        select reset_code from am_msuser amu 
        where amu.hashed_id_no = %s 
        '''

        hashedNIK = hashlib.sha256(NIK.encode()).hexdigest()

        cursor.execute(query, (hashedNIK, ))

        result = cursor.fetchone()

        if result is None:
            resetCode = ""
        else : 
            resetCode=  result[0]
        
        connection.commit()

    except (Exception, psycopg2.DatabaseError) as error:
        print(f"Error occurred: {error}")
    finally:
        if connection:
            cursor.close()
            connection.close()

    return resetCode


def updateUserOtpCode():
    with open('data/user.js', 'r') as file:
        user = file.read()

    context = execjs.compile(user)
    users = context.eval('user')

    for user in users:
        NIK = user['nik']
        
        otpCode = getOtpCode(NIK)
        print(otpCode)

        if otpCode:
            user['otp'] = otpCode

    with open('data/user.js', 'w') as file:
        file.write("const user = \n")
        file.write("    [\n")
        
        for user in users:
            file.write("        {\n")
            file.write(f"            nik: '{user['nik']}',\n")
            file.write(f"            date: '{user['date']}',\n")
            file.write(f"            username: '{user['username']}',\n")
            file.write(f"            nohp: '{user['nohp']}',\n")
            file.write(f"            msg: '{user['msg']}',\n")
            file.write(f"            otp: '{user['otp']}',\n")
            file.write(f"            documentId: '{user['documentId']}',\n")
            file.write(f"            refNumber: '{user['refNumber']}',\n")
            file.write(f"            idSigningProcessAuditTrailDetail: '{user['idSigningProcessAuditTrailDetail']}',\n")
            file.write(f"            bearerToken: '{user['bearerToken']}'\n")
            file.write("        },\n")
        
        file.write("    ];\n")
        file.write("module.exports = user;\n")

def updateUserResetCodeCode():
    with open('data/user.js', 'r') as file:
        user = file.read()

    context = execjs.compile(user)
    users = context.eval('user')

    for user in users:
        NIK = user['nik']
        
        resetCode = getResetCode(NIK)
        print(resetCode)

        if resetCode:
            user['otp'] = resetCode

    with open('data/user.js', 'w') as file:
        file.write("const user = \n")
        file.write("    [\n")
        
        for user in users:
            file.write("        {\n")
            file.write(f"            nik: '{user['nik']}',\n")
            file.write(f"            date: '{user['date']}',\n")
            file.write(f"            username: '{user['username']}',\n")
            file.write(f"            nohp: '{user['nohp']}',\n")
            file.write(f"            msg: '{user['msg']}',\n")
            file.write(f"            otp: '{user['otp']}',\n")
            file.write(f"            documentId: '{user['documentId']}',\n")
            file.write(f"            refNumber: '{user['refNumber']}',\n")
            file.write(f"            idSigningProcessAuditTrailDetail: '{user['idSigningProcessAuditTrailDetail']}',\n")
            file.write(f"            bearerToken: '{user['bearerToken']}'\n")
            file.write("        },\n")
        
        file.write("    ];\n")
        file.write("module.exports = user;\n")

# updateUserResetCodeCode()
# updateUserOtpCode()