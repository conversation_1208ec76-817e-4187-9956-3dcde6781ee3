import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import APIS from "../data/apis.js";
import baseurl from "../data/baseurl.js";
import auths from "../data/auth.js";


let baseurls = baseurl[0];
const url = baseurls.url +"/services/autosign/s/detailImportBmAutosign";
let auth = auths[3];
const SLEEP_DURATION = 5;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
        }
    };

    let user = users[exec.vu.idInTest - 1];
 
    if (user == null) {
        user = {
        "username": ""
        , "nik": ""
        , "nohp": ""
        };
    }

    const requestBody = JSON.stringify({

        audit: { 
            callerId: user.username
        },
        fileName: "templateImportBm.xlsx",
        requestDate : "2025-01-08 10:47:51.641",
        tenantCode : "ADINS",
        page: 1
    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(response.status);




}
