import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import userManagementData from "./Data/userManagementData.js";
import baseurl from "./Data/baseurl.js";
import authorization from "./Data/authorization-library.js";

// Configuration
let baseurls = baseurl[0];
const url = baseurls.url + "/services/user-management/s/insertUserManagement";
const SLEEP_DURATION = 0.5;

// Use <EMAIL> for authorization (index 3)
const authData = authorization[3];

// Small test with safe configuration - 20 users starting from index 200
export let options = {
    discardResponseBodies: false,
    scenarios: {
        insert_users_safe_test: {
            executor: 'shared-iterations',
            vus: 2,            // 2 virtual users
            iterations: 20,    // 20 iterations total
            maxDuration: '5m', // 5 minutes max duration
        },
    },
    thresholds: {
        http_req_duration: ['p(95)<10000'], // 95% of requests should be below 10s
        http_req_failed: ['rate<0.2'],      // Error rate should be below 20% for testing
    },
};

// Test scenario
export default function () {
    // Use scenario iteration counter directly + offset to avoid duplicates
    const globalIndex = 200 + exec.scenario.iterationInInstance;
    
    // Safety check: ensure index is within bounds
    if (globalIndex >= userManagementData.totalUsers) {
        console.error(`Index ${globalIndex} exceeds available data (${userManagementData.totalUsers}). Skipping.`);
        return;
    }
    
    // Get user data by global index
    const userData = userManagementData.getUserByIndex(globalIndex);
    
    if (!userData) {
        console.error(`No user data found for index ${globalIndex}`);
        return;
    }

    console.log(`Processing user ${globalIndex + 1}: LoginId=${userData.loginId}, FullName=${userData.fullName}`);

    // Request headers
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': authData.bearerToken
        }
    };

    // Request body with correct configuration
    const requestBody = JSON.stringify({
        "audit": {
            "callerId": "<EMAIL>"
        },
        "fullName": userData.fullName,
        "tenantCode": "ADINS",
        "loginId": userData.loginId,
        "roleCode": "USER_EDITOR",
        "password": "ADINS",
        "officeCode": "0191"
    });

    // Add delay to avoid overwhelming the server
    sleep(SLEEP_DURATION);

    // Make the request
    const response = http.post(url, requestBody, requestHeader);

    // Check response
    const isSuccess = check(response, {
        'is status 200': (r) => r.status === 200,
        'response has success status': (r) => {
            try {
                const body = JSON.parse(r.body);
                return body.status && body.status.code === 0;
            } catch (e) {
                return false;
            }
        }
    });

    // Log results
    if (isSuccess) {
        console.log(`✓ Successfully inserted user ${globalIndex + 1} (${userData.loginId})`);
    } else {
        console.error(`✗ Failed to insert user ${globalIndex + 1} (${userData.loginId}): Status ${response.status}`);
        
        // Try to parse error message
        try {
            const errorBody = JSON.parse(response.body);
            if (errorBody.status && errorBody.status.message) {
                console.log(`Error: ${errorBody.status.message}`);
            }
        } catch (e) {
            // Ignore JSON parse errors
        }
    }
}

// Setup function
export function setup() {
    console.log("=== InsertUserManagement Safe Test Setup ===");
    console.log(`Target URL: ${url}`);
    console.log(`Total available users: ${userManagementData.totalUsers}`);
    console.log(`Authorization: ${authData.username}`);
    console.log(`VUs: 2`);
    console.log(`Total iterations: 20`);
    console.log(`Starting from user index: 200 (to avoid duplicates)`);
    console.log(`Executor: shared-iterations (safe mode)`);
    
    // Test data access
    console.log("\nTesting data access...");
    for (let i = 200; i < 203; i++) {
        const testUser = userManagementData.getUserByIndex(i);
        if (testUser) {
            console.log(`  User ${i}: LoginId=${testUser.loginId}, FullName=${testUser.fullName}`);
        } else {
            console.error(`  User ${i}: No data found`);
        }
    }
    
    console.log("Setup complete. Starting safe test...");
    return {};
}

// Teardown function
export function teardown(data) {
    console.log("=== InsertUserManagement Safe Test Complete ===");
    console.log("This was a small safe test. Check results before running full load test.");
}
