import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import APIS from "../apis.js";
import tenant from "./Data/tenant-library.js";
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const url = baseurls.url  + "/services/user/getUrlForwarder";
const SLEEP_DURATION = 2;
const USERNAME = "<EMAIL>";
const PASSWORD = "supersupeR4!";
const USERNAME_2 = "<EMAIL>";


// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 200,
            iterations: 2,
            maxDuration: '10m',
        },
    },
};

// Test scenario
export default function () {
    let tenantKey = tenant[0][1];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': tenantKey['xapikey']
            //  ,'Cookie' : 'idjs=FDD47A1F028E0E6900095FC1D1395777'
        }
    };

    // Random e-mail suffix

    let apis = APIS[0][exec.vu.idInTest - 1];

    if (apis == null) {
        apis = {
            "username": ""
            , "nik": ""
            , "nohp": ""
        };
    }
    // Get User Profile (user/s/profiles)
    const requestBody = JSON.stringify({

        audit: { callerId: "UserIBAA" }
        , urlCode: "5uDgZBAd8PIZ4qtl866rlA=="
        

    });

    const response = http.post(url, requestBody, requestHeader);
  
    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200

    });

    console.log(response.body);

}
