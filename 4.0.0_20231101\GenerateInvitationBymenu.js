import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import APIS from "./Data/apis.js";
import baseurl from "./Data/baseurl.js"

let baseurls = baseurl[0]

const SLEEP_DURATION = 5;

// setup ramping
// export let options = {
//   discardResponseBodies: false,
//   scenarios: {

//     contacts: {
//       executor: 'ramping-vus',
//       startVUs: 1,

//       stages: [

//         { duration: '1m', target: 20 },
//         { duration: '1m', target: 20 },
//         { duration: '5s', target: 0 }
//       ],
//       gracefulRampDown: '0s',
//     },
//   },
// };

// setup per iteration
export let options = {
  discardResponseBodies: false,
  scenarios: {

   contacts: {
      executor: 'per-vu-iterations',
      vus: 20,
      iterations: 1,
      maxDuration: '10m',
    },
  },
};

// Test scenario
export default function () {
  const params = {
    headers: {
      'Content-Type': 'application/json'
    }
  };

  const paramsgenerateinvitation = {
    headers: {
      'Content-Type': 'application/json'
      , 'x-api-key': 'Gy6Ka5XhgRmq@ADINS'
      //  ,'Cookie' : 'idjs=FDD47A1F028E0E6900095FC1D1395777'
    }
  };

  // Random e-mail suffix

  let apis = APIS[0][exec.vu.idInTest];
 
  if (apis == null) {
    apis = {
      "username": ""
      , "nik": ""
      , "nohp": ""
    };
  }
  // Get User Profile (user/s/profiles)
  const generateLinkInvitationRequest = JSON.stringify({

    audit: { callerId: apis.username }
    , tenantCode: "ADINS"
    , users: [{
      nama: apis.username,
      tlp: apis.nohp  + [exec.vu.idInTest],
      email: "MALVINTEST025" + [exec.vu.idInTest ] + "@esignhub.my.id",
      jenisKelamin: "M",
      tmpLahir: "BOGOR",
      tglLahir: "1980-01-01",
      idKtp: apis.nik,
      provinsi: "Jawa Barat",
      kota: "Bogor",
      kecamatan: "Bogor Selatan",
      kelurahan: "Baranangsiang",
      kodePos: "16143",
      alamat: "JL. SAWOKNA NO.1000 BANTAR KEMANG"
      , vendorCode: "PRIVY"
    }]


  });
  //const generateLinkInvitation = http.post('https://esignhub.docsol.id:543/adimobile/esign/services/user/generateInvitationLink', generateLinkInvitationRequest, paramsgenerateinvitation);
  const generateLinkInvitation = http.post(baseurls.url +'/services/user/generateInvitationLink', generateLinkInvitationRequest, paramsgenerateinvitation);
    console.log(generateLinkInvitation.body());
  sleep(SLEEP_DURATION);

  check(generateLinkInvitation, {
    'is status 200': (r) => r.status === 200 
    
  });

}
