import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import unsignDocument from "./Data/unsignDocumentId.js";
import authorizations from "./Data/authorization-library.js";
import baseurl from "./Data/baseurl.js";


let baseurls = baseurl[0];
const url = baseurls.url + "/services/user/s/sentOtpSigningVerification";

const SLEEP_DURATION = 5;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {
    
    let listDoc = unsignDocument[(exec.vu.idInInstance - 1) % unsignDocument.length];
    let authorization = authorizations[0];

    const requestHeader = {

        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            ,'Authorization': authorization.bearerToken


        }
    };

  
    const requestBody = JSON.stringify({
        audit:{
            callerId: authorization.username
        },
        vendorCode: "VIDA",
        tenantCode: "ADINS",
        sendingPointOption : "SMS",
        phoneNo : "08129772389",
        documentId : [listDoc.docId]

    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(response.body);




}
