import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import tenants from "../data/apiKey.js";
import baseurl from "../data/baseurl.js";
import auth from "../data/auth.js"

let baseurls = baseurl[0];
const url = baseurls.url  + "/services/autosign/s/detailImportBmAutosign";
const SLEEP_DURATION = 0;
let authData = auth[3];



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            ,'Authorization': authData.bearerToken
        }
    };

    const requestBody = JSON.stringify({

        audit: { callerId:"<EMAIL>" },
        fileName: "templateImportBm (1).xlsx",
        page: 1,
        requestDate: "2024-12-26 23:22:09.718",
        tenantCode: "ADINS"
        
    }, null, "\t");

 
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(response.body);

    sleep(SLEEP_DURATION);


}
