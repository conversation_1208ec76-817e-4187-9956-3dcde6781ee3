import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';

import auth from "./Data/authorization-library.js";
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const URL = baseurls.url  + "/services/document/s/retryStampingMeterai";
const SLEEP_DURATION = 2;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '10m',
        },
    },
};

// Test scenario
export default function () {
    let auths = auth[0][0];
    const requestHeaeder = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'Authorization': auths['bearerToken']
            //  ,'Cookie' : 'idjs=FDD47A1F028E0E6900095FC1D1395777'
        }
    };


    // Get User Profile (user/s/profiles)
    const requestBody = JSON.stringify({

        audit: { callerId: "<EMAIL>" }
        , refNumber: "Test-JS-Loadtest-Vida-00" + [exec.vu.idInTest]
        , tenantCode: "WOMF"


    });
    const apiResponse = http.post(url, requestBody, requestHeaeder);
  
    sleep(SLEEP_DURATION);

    check(apiResponse, {
        'is status 200': (r) => r.status === 200

    });

    console.log(apiResponse.body);

}
