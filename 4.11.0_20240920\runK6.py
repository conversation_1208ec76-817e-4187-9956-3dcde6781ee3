import subprocess
import json
import os
import pandas as pd
import numpy as np
from openpyxl import load_workbook
from datetime import datetime
import execjs
import requests
import psycopg2
from psycopg2 import sql

def update_proses_materai(documentId, prosesMaterai):
    db_config = {
        'dbname': 'esign',
        'user': 'super1',
        'password': 'Super123!',
        'host': 'pgm-d9j5s6qm0w5l11z4zo.pgsql.ap-southeast-5.rds.aliyuncs.com',
        'port': '5432'
    }

    try:
        connection = psycopg2.connect(**db_config)
        cursor = connection.cursor()

        query = '''
        UPDATE tr_document_h
        SET proses_materai = %s
        WHERE REF_NUMBER = ANY(%s)
        '''
        cursor.execute(query, (prosesMaterai, documentId))

        connection.commit()

        print(f"Updated proses_materai for document_id: {documentId}")

    except (Exception, psycopg2.DatabaseError) as error:
        print(f"Error occurred: {error}")
    finally:
        if connection:
            cursor.close()
            connection.close()

def resetSignedDocumentStampingStatus():
    try:
        with open('Data/DocumentId.js', 'r') as file:
            js_code = file.read()

        context = execjs.compile(js_code)
        documentIds = context.eval('signDoc')

        docIdArray = [documentId['refNumber'] for documentId in documentIds]

        if docIdArray:
                update_proses_materai(docIdArray, 0)
        else:
            print("No document IDs found to update.")

    except FileNotFoundError as e:
        print(f"JavaScript file not found: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")

def failedSignedDocumentStampingStatus():
    try:
        with open('Data/DocumentId.js', 'r') as file:
            js_code = file.read()

        context = execjs.compile(js_code)
        documentIds = context.eval('signDoc')

        docIdArray = [documentId['refNumber'] for documentId in documentIds]

        if docIdArray:
                update_proses_materai(docIdArray, 71)
        else:
            print("No document IDs found to update.")

    except FileNotFoundError as e:
        print(f"JavaScript file not found: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")

def run_k6_and_save_output(k6_script_path, output_json_path='output.json'):
    try:
        result = subprocess.run(
            ['k6', 'run', '--out', f'json={output_json_path}', k6_script_path],
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            print(f"Error running k6 script: {result.stderr}")
            return None
        
        output = result.stdout
        print("K6 Output:")
        print(output)

        if not os.path.exists(output_json_path):
            print(f"Output JSON file '{output_json_path}' not found.")
            return None

        json_objects = []
        with open(output_json_path, 'r') as file:
            for line in file:
                try:
                    json_object = json.loads(line)
                    json_objects.append(json_object)
                except json.JSONDecodeError:
                    continue
        
        if json_objects:
            combined_json = json_objects
        else:
            print("No valid JSON objects found in the output file.")
            return None
        
        return combined_json

    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return None
    
def extract_and_calculate_metrics(file_path):
    try:
        with open(file_path, 'r') as file:
            json_lines = file.readlines()

        data = [json.loads(line) for line in json_lines]

        values = [
            entry['data']['value']
            for entry in data
            if entry.get('metric') == 'http_req_duration' and entry.get('type') == 'Point'
        ]

        if not values:
            print("No matching data found.")
            return None

        min_value = int(min(values))
        max_value = int(max(values))
        average_value = int(sum(values) / len(values))
        p95_value = int(np.percentile(values, 95))

        return {
            'min': min_value,
            'max': max_value,
            'average': average_value,
            'p95': p95_value
        }

    except FileNotFoundError:
        print(f"File '{file_path}' not found.")
        return None
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON: {e}")
        return None
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return None

def create_excel_report(index, metrics, api_name, output_path):
    current_datetime = datetime.now()
    current_date = current_datetime.strftime('%d/%m/%Y')
    current_time = current_datetime.strftime('%H:%M')
    
    new_data = [
        [index, api_name, "Read", 20, metrics['min'], metrics['max'], metrics['average'], metrics['p95'], 
         current_date, current_time, None, None, None]
    ]

    if os.path.exists(output_path):
        workbook = load_workbook(output_path)
        sheet = workbook.active

        next_row = sheet.max_row + 1

        for row_data in new_data:
            for col_index, value in enumerate(row_data, start=1):
                sheet.cell(row=next_row, column=col_index, value=value)
        
        workbook.save(output_path)
        print(f"Data appended successfully to {output_path}.")
    
    else:
        columns = [
            "No", "API", "Request Type", "Count", "min (ms)", "max (ms)", "mean (ms)", "p95 (ms)",
            "Date", "time", "Resource", "Status", "Note"
        ]
        
        df = pd.DataFrame(new_data, columns=columns)
        df.to_excel(output_path, index=False)
        print(f"Excel report created: {output_path}")


def getBearer(baseurl, username, password):
    url = baseurl +'/oauth/token'
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:131.0) Gecko/20100101 Firefox/131.0',
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
    data = {
        'client_id': 'frontend',
        'grant_type': 'password',
        'username': username,
        'password': password
    }

    response = requests.post(url, headers=headers, data=data)
    if response.status_code == 200:
        return response.json().get('access_token')
    else:
        print(f"Failed to get token for {username}. Status code: {response.status_code}")
        print(url)
        return None
    
def getMsg(username):
    url = 'https://mockdata.gyandi.dev/encrypt'
    headers = {
        'Accept': 'application/json, */*',
        'Content-Type': 'application/json'
    }
    data = {
        "tenantCode": "ADINS",
        "officeCode": "HO",
        "email": username,
        "aesKey" : "CniQHdpCOsruzNcv",
        "menu" : "inquiry",
        "env" : "DEV"
    }

    response = requests.post(url, headers=headers, json=data)
    if response.status_code == 200:
        return response.json().get('encrypt_msg')
    else:
        print(f"Failed to get msg for {username}. Status code: {response.status_code}")
        return None
    
def encrypt(docId):
    url = 'https://mockdata.gyandi.dev/encrypt_documentId'

    headers = {
        'Accept': 'application/json, */*',
        'Content-Type': 'application/json'
    }

    data = {
    "documentId" : docId,
    "aesKey" : "CniQHdpCOsruzNcv"
    }

    response = requests.post(url, headers=headers, json=data)
    if response.status_code == 200:
        return response.json().get('encrypt_msg')
    else:
        print(f"Failed to get msg for {docId}. Status code: {response.status_code}")
        return None

def updateAuthorization() :
    with open('Data/authorization-library.js', 'r') as file:
        js_code = file.read()

    with open('Data/baseurl.js', 'r') as fileurl:
        js_code_url = fileurl.read()    

    # Compile and execute JavaScript
    context = execjs.compile(js_code)
    authorizations = context.eval('authorization')

    context = execjs.compile(js_code_url)
    urls = context.eval('baseurl')

    baseurl = urls[0]['url']

    for authorization in authorizations:
        username = authorization['username']
        password = authorization['password']
        newBearerToken = getBearer(baseurl, username, password)
        msg = getMsg(username)

        if newBearerToken:
            authorization['bearerToken'] = 'Bearer ' + newBearerToken

        if msg:
            authorization['msg'] = msg

    with open('Data/authorization-library.js', 'w') as file:
        file.write("const authorization = \n")
        file.write("    [\n")
        for authorization in authorizations:
            file.write("        {\n")
            file.write(f"            username: '{authorization['username']}',\n")
            file.write(f"            password: '{authorization['password']}',\n")
            file.write(f"            bearerToken: '{authorization['bearerToken']}',\n")
            file.write(f"            msg: '{authorization['msg']}'\n")
            file.write("        },\n")
        file.write("    ];\n")
        file.write("module.exports = authorization;\n")

    for index, authorization in enumerate(authorizations):
        username = authorization['username']
        password = authorization['password']
        bearer_token = authorization['bearerToken']
        msg = authorization['msg']

        print(f"Authorization {index + 1}:")
        print(f"  Username: {username}")
        print(f"  Password: {password}")
        print(f"  Bearer Token: {bearer_token}")
        print(f"  Message: {msg}")
        print()

def updateDocumentIdEncrypt() :
    with open('Data/unsignDocumentId.js', 'r') as file:
        js_code = file.read()

    with open('Data/DocumentId.js', 'r') as file:
        js_code_signed = file.read()

    context = execjs.compile(js_code)
    unsignDocuments = context.eval('encryptedDocId')

    context = execjs.compile(js_code_signed)
    signedDocuments = context.eval('signDoc')

    for unsignDocument in unsignDocuments:
        documentId = unsignDocument['docId']
        msg = encrypt(documentId)
        
        if msg:
            unsignDocument['encryptedDocId'] = msg
    
    for signedDocument in signedDocuments:
        documentId = signedDocument['docId']
        msg = encrypt(documentId)
        
        if msg:
            unsignDocument['encryptedDocId'] = msg

    with open('Data/unsignDocumentId.js', 'w') as file:
        file.write("const encryptedDocId = \n")
        file.write("    [\n")
        for unsignDocument in unsignDocuments:
            file.write("        {\n")
            file.write(f"            refNumber: '{unsignDocument['refNumber']}',\n")
            file.write(f"            docId: '{unsignDocument['docId']}',\n")
            file.write(f"            encryptedDocId: '{unsignDocument['encryptedDocId']}'\n")
            file.write("        },\n")
        file.write("    ];\n")
        file.write("module.exports = encryptedDocId;\n")
    
    with open('Data/DocumentId.js', 'w') as file:
        file.write("const signDoc = \n")
        file.write("    [\n")
        for signedDocument in signedDocuments:
            file.write("        {\n")
            file.write(f"            refNumber: '{signedDocument['refNumber']}',\n")
            file.write(f"            docId: '{signedDocument['docId']}',\n")
            file.write(f"            encryptedDocId: '{signedDocument['encryptedDocId']}'\n")
            file.write("        },\n")
        file.write("    ];\n")
        file.write("module.exports = signDoc;\n")

    for index, unsignDocument in enumerate(unsignDocuments):
        refNumber = unsignDocument['refNumber']
        documentId = unsignDocument['docId']
        msg = unsignDocument['encryptedDocId']

        print(f"unsignDocument {index + 1}:")
        print(f"  refNumber: {refNumber}")
        print(f"  documentId: {documentId}")
        print(f"  encryptedDocId: {msg}")
        print()
    
    for index, signedDocument in enumerate(signedDocuments):
        refNumber = signedDocument['refNumber']
        documentId = signedDocument['docId']
        msg = signedDocument['encryptedDocId']

        print(f"unsignDocument {index + 1}:")
        print(f"  refNumber: {refNumber}")
        print(f"  documentId: {documentId}")
        print(f"  encryptedDocId: {msg}")
        print()

if __name__ == "__main__":

    updateAuthorization()
    updateDocumentIdEncrypt()

    k6_script_path = [
            {
                "jsPath": "GenerateInvitationBymenu.js",
                "needFunction": ""
            },
             {
                "jsPath": "GenerateInvitation.js",
                "needFunction": ""
            },
            {
                "jsPath": "GenerateInvitationLinkExternal.js",
                "needFunction": ""
            },
            {
                "jsPath": "SendDocExternal.js",
                "needFunction": ""
            },
            {
                "jsPath": "SendDocNormal.js",
                "needFunction": ""
            },
            {
                "jsPath": "SendManualDoc.js",
                "needFunction": ""
            }
    ]

    output_json_path = 'Data/output.json'
    excel_output_path = 'Data/loadTestReport.xlsx'

    for index, script_info  in enumerate(k6_script_path) :
        if script_info["needFunction"]:
            function_to_call = globals().get(script_info["needFunction"])
            if function_to_call:
                function_to_call()

        data = run_k6_and_save_output(script_info["jsPath"], output_json_path)
        script_name = os.path.basename(script_info["jsPath"]) 
        api_name = os.path.splitext(script_name)[0]

        if data:
            print("JSON output successfully retrieved and printed.")
            metrics = extract_and_calculate_metrics(output_json_path)
            print(f"Min: {metrics['min']:.2f}")
            print(f"Max: {metrics['max']:.2f}")
            print(f"Average: {metrics['average']:.2f}")
            print(f"95th Percentile (p95): {metrics['p95']:.2f}")
            create_excel_report(index + 1, metrics, api_name, excel_output_path)
        else:
            print("Could not retrieve JSON output.")
