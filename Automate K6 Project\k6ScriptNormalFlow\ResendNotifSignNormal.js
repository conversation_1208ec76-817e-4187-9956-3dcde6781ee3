import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import baseurl from "../data/baseurl.js";
import users from "../data/user.js";
import auths from "../data/auth.js";

let baseurls = baseurl[0];
const url = baseurls.url + "/services/document/s/resendNotifSign";
let auth = auths[3];

const SLEEP_DURATION = 5;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {

    let user = users[exec.vu.idInTest - 1];
 
    if (user == null) {
        user = {
        "username": ""
        , "nik": ""
        , "nohp": ""
        };
    }

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            ,'Authorization': auth.bearerToken
        }
    };

    const requestBody = JSON.stringify({
        audit:{
            callerId: auth.username
        },
        documentId : user.documentId
    }, null, "\t");

    const response = http.post(url, requestBody, requestHeader);
    console.log(response.body);
    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200,

    });

}
