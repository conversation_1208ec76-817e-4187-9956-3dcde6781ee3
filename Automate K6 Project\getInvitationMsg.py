import psycopg2
from psycopg2 import sql
from dotenv import load_dotenv
import os
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import base64
import re
import execjs
import json

load_dotenv()

def getInvitationCode(NIK):

    db_config = {
        'dbname': os.getenv('dataSourceName'),
        'user': os.getenv('dataSourceUsername'),
        'password': os.getenv('dataSourcePassword'),
        'host': os.getenv('dataSourceUrl'),
        'port': os.getenv('dataSourcePort')
    }

    try:
        connection = psycopg2.connect(**db_config)
        cursor = connection.cursor()

        query = '''
        select invitation_code from tr_invitation_link til 
        where til.id_no =  %s
        '''
        cursor.execute(query, (NIK, ))

        result = cursor.fetchone()

        if result is None:
            invitationCode = ""
        else : 
            invitationCode=  result[0]
        
        connection.commit()

    except (Exception, psycopg2.DatabaseError) as error:
        print(f"Error occurred: {error}")
    finally:
        if connection:
            cursor.close()
            connection.close()

    return invitationCode

def encrpyt(data):

    key = os.getenv('aesKey')

    cipher = AES.new(key.encode("utf-8"), AES.MODE_ECB)
    plaintext = data.encode("utf-8")
    paddedPlaintext = pad(plaintext, AES.block_size)
    encryptedPlaintext = cipher.encrypt(paddedPlaintext)
    encryptedBase64 = base64.b64encode(encryptedPlaintext).decode("utf-8")
    return encryptedBase64

def updateUserInvitation():
    with open('data/user.js', 'r') as file:
        user = file.read()

    context = execjs.compile(user)
    users = context.eval('user')

    for user in users:
        NIK = user['nik']
        
        encryptedInvitation = encrpyt(getInvitationCode(NIK))

        if encryptedInvitation:
            user['msg'] = encryptedInvitation

    with open('data/user.js', 'w') as file:
        file.write("const user = \n")
        file.write("    [\n")
        
        for user in users:
            file.write("        {\n")
            file.write(f"            nik: '{user['nik']}',\n")
            file.write(f"            date: '{user['date']}',\n")
            file.write(f"            username: '{user['username']}',\n")
            file.write(f"            nohp: '{user['nohp']}',\n")
            file.write(f"            msg: '{user['msg']}',\n")
            file.write(f"            otp: '{user['otp']}',\n")
            file.write(f"            documentId: '{user['documentId']}',\n")
            file.write(f"            refNumber: '{user['refNumber']}',\n")
            file.write(f"            idSigningProcessAuditTrailDetail: '{user['idSigningProcessAuditTrailDetail']}',\n")
            file.write(f"            bearerToken: '{user['bearerToken']}'\n")
            file.write("        },\n")
        
        file.write("    ];\n")
        file.write("module.exports = user;\n")

# updateUserInvitation()