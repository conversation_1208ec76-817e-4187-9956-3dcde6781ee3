import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import userManagementData from "./Data/userManagementData.js";
import baseurl from "./Data/baseurl.js";
import authorization from "./Data/authorization-library.js";

// Configuration
let baseurls = baseurl[0];
const url = baseurls.url + "/services/user-management/s/insertUserManagement";
const SLEEP_DURATION = 0.5; // Slightly longer sleep for stability

// Use <EMAIL> for authorization (index 3)
const authData = authorization[3];

// Continue from index 167500 - remaining users: 832500
const START_INDEX = 167500;
const REMAINING_USERS = 1000000 - START_INDEX; // 832500 users remaining

// Safe configuration - continue from where we left off
export let options = {
    discardResponseBodies: false,
    scenarios: {
        insert_users_continue: {
            executor: 'shared-iterations',
            vus: 50,            // 50 virtual users
            iterations: REMAINING_USERS, // 832500 remaining iterations
            maxDuration: '180m', // 3 hours max duration
        },
    },
    thresholds: {
        http_req_duration: ['p(95)<10000'], // 95% of requests should be below 10s
        http_req_failed: ['rate<0.1'],      // Error rate should be below 10%
    },
};

// Test scenario
export default function () {
    // Use scenario iteration counter + start index
    const globalIndex = START_INDEX + exec.scenario.iterationInInstance;
    
    // Safety check: ensure index is within bounds
    if (globalIndex >= userManagementData.totalUsers) {
        console.error(`Index ${globalIndex} exceeds available data (${userManagementData.totalUsers}). Skipping.`);
        return;
    }
    
    // Get user data by global index
    const userData = userManagementData.getUserByIndex(globalIndex);
    
    if (!userData) {
        console.error(`No user data found for index ${globalIndex}`);
        return;
    }

    // Request headers
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': authData.bearerToken
        }
    };

    // Request body with correct configuration
    const requestBody = JSON.stringify({
        "audit": {
            "callerId": "<EMAIL>"
        },
        "fullName": userData.fullName,
        "tenantCode": "ADINS",
        "loginId": userData.loginId,
        "roleCode": "USER_EDITOR",
        "password": "ADINS",
        "officeCode": "0191"
    });

    // Add delay to avoid overwhelming the server
    sleep(SLEEP_DURATION);

    // Make the request with retry logic
    let response;
    let retryCount = 0;
    const maxRetries = 3;

    do {
        response = http.post(url, requestBody, requestHeader);
        
        // If successful, break out of retry loop
        if (response.status === 200) {
            break;
        }
        
        // If it's a client error (4xx), don't retry (except 429)
        if (response.status >= 400 && response.status < 500 && response.status !== 429) {
            break;
        }
        
        // For server errors or rate limiting, wait and retry
        if (retryCount < maxRetries && (response.status >= 500 || response.status === 429 || response.status === 0)) {
            retryCount++;
            const backoffTime = Math.min(60, Math.pow(2, retryCount) + Math.random() * 5);
            console.log(`Retry ${retryCount}/${maxRetries} for user ${globalIndex + 1} after ${backoffTime.toFixed(1)}s`);
            sleep(backoffTime);
        }
        
    } while (retryCount < maxRetries && (response.status >= 500 || response.status === 429 || response.status === 0));

    // Check response
    const isSuccess = check(response, {
        'is status 200': (r) => r.status === 200,
        'response has success status': (r) => {
            try {
                const body = JSON.parse(r.body);
                return body.status && body.status.code === 0;
            } catch (e) {
                return false;
            }
        }
    });

    // Log progress every 1000 requests
    if ((globalIndex - START_INDEX) % 1000 === 0) {
        const progress = ((globalIndex - START_INDEX + 1) / REMAINING_USERS * 100).toFixed(2);
        console.log(`Progress: User ${globalIndex + 1} (${progress}% of remaining) - LoginId: ${userData.loginId}, Status: ${response.status}`);
    }

    // Log errors for debugging
    if (!isSuccess) {
        console.error(`Failed to insert user ${globalIndex + 1} (${userData.loginId}): Status ${response.status}`);
        
        // Try to parse error message
        try {
            const errorBody = JSON.parse(response.body);
            if (errorBody.status && errorBody.status.message) {
                console.log(`Error: ${errorBody.status.message}`);
            }
        } catch (e) {
            // Ignore JSON parse errors
        }
    }
}

// Setup function to validate data availability
export function setup() {
    console.log("=== InsertUserManagement Continue Load Test Setup ===");
    console.log(`Target URL: ${url}`);
    console.log(`Starting from index: ${START_INDEX}`);
    console.log(`Remaining users to insert: ${REMAINING_USERS}`);
    console.log(`Total users available: ${userManagementData.totalUsers}`);
    console.log(`Authorization: ${authData.username}`);
    console.log(`VUs: 50`);
    console.log(`Total iterations: ${REMAINING_USERS}`);
    console.log(`Executor: shared-iterations (continue mode)`);
    console.log(`Estimated duration: ${(REMAINING_USERS * SLEEP_DURATION / 50 / 60).toFixed(0)} minutes`);
    
    // Validate that we have enough user data
    if (userManagementData.totalUsers < 1000000) {
        throw new Error(`Insufficient user data. Need 1000000 users, but only have ${userManagementData.totalUsers}`);
    }
    
    // Test a sample request to validate configuration
    console.log("Testing sample request...");
    const sampleUser = userManagementData.getUserByIndex(START_INDEX);
    if (!sampleUser) {
        throw new Error(`No sample user data available at index ${START_INDEX}`);
    }
    
    console.log(`Sample user: Index=${START_INDEX}, LoginId=${sampleUser.loginId}, FullName=${sampleUser.fullName}`);
    
    // Show range of users to be processed
    const endIndex = Math.min(START_INDEX + REMAINING_USERS - 1, userManagementData.totalUsers - 1);
    const endUser = userManagementData.getUserByIndex(endIndex);
    if (endUser) {
        console.log(`End user: Index=${endIndex}, LoginId=${endUser.loginId}, FullName=${endUser.fullName}`);
    }
    
    console.log("Setup complete. Starting continuation load test...");
    return {};
}

// Teardown function for final reporting
export function teardown(data) {
    console.log("=== InsertUserManagement Continue Load Test Complete ===");
    console.log(`Processed users from index ${START_INDEX} onwards`);
    console.log("Check the K6 summary for detailed metrics.");
    console.log("Review logs for any failed insertions that may need manual retry.");
}
