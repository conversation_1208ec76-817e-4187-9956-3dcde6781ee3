import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import userManagementData from "./Data/userManagementData.js";
import baseurl from "./Data/baseurl.js";
import authorization from "./Data/authorization-library.js";

// Configuration
let baseurls = baseurl[0];
const url = baseurls.url + "/services/user-management/s/insertUserManagement";
const SLEEP_DURATION = 2.0; // Longer sleep for server recovery

// Use <EMAIL> for authorization (index 3)
const authData = authorization[3];

// Continue from index 167500 - remaining users: 832500
const START_INDEX = 167500;
const REMAINING_USERS = 1000000 - START_INDEX; // 832500 users remaining

// Conservative configuration - lower load to help server recover
export let options = {
    discardResponseBodies: false,
    scenarios: {
        insert_users_continue_conservative: {
            executor: 'shared-iterations',
            vus: 20,            // Reduced from 50 to 20 VUs
            iterations: REMAINING_USERS, // 832500 remaining iterations
            maxDuration: '300m', // 5 hours max duration (longer for conservative approach)
        },
    },
    thresholds: {
        http_req_duration: ['p(95)<15000'], // More lenient - 15s
        http_req_failed: ['rate<0.2'],      // Allow 20% failure rate due to server issues
    },
};

// Test scenario
export default function () {
    // Use scenario iteration counter + start index
    const globalIndex = START_INDEX + exec.scenario.iterationInInstance;
    
    // Safety check: ensure index is within bounds
    if (globalIndex >= userManagementData.totalUsers) {
        console.error(`Index ${globalIndex} exceeds available data (${userManagementData.totalUsers}). Skipping.`);
        return;
    }
    
    // Get user data by global index
    const userData = userManagementData.getUserByIndex(globalIndex);
    
    if (!userData) {
        console.error(`No user data found for index ${globalIndex}`);
        return;
    }

    // Request headers
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': authData.bearerToken
        }
    };

    // Request body with correct configuration
    const requestBody = JSON.stringify({
        "audit": {
            "callerId": "<EMAIL>"
        },
        "fullName": userData.fullName,
        "tenantCode": "ADINS",
        "loginId": userData.loginId,
        "roleCode": "USER_EDITOR",
        "password": "ADINS",
        "officeCode": "0191"
    });

    // Add delay to avoid overwhelming the server
    sleep(SLEEP_DURATION);

    // Make the request with aggressive retry logic for server recovery
    let response;
    let retryCount = 0;
    const maxRetries = 5; // Increased retries

    do {
        response = http.post(url, requestBody, requestHeader);
        
        // If successful, break out of retry loop
        if (response.status === 200) {
            try {
                const body = JSON.parse(response.body);
                if (body.status && body.status.code === 0) {
                    break; // Success
                }
            } catch (e) {
                // Continue to retry if can't parse response
            }
        }
        
        // If it's a client error (4xx), don't retry (except 429)
        if (response.status >= 400 && response.status < 500 && response.status !== 429) {
            break;
        }
        
        // For server errors, connection issues, or rate limiting, wait and retry
        if (retryCount < maxRetries && (response.status >= 500 || response.status === 429 || response.status === 0)) {
            retryCount++;
            // Exponential backoff with jitter - longer waits for server recovery
            const baseBackoff = Math.pow(2, retryCount) * 5; // 10s, 20s, 40s, 80s, 160s
            const jitter = Math.random() * 10; // 0-10s random
            const backoffTime = Math.min(180, baseBackoff + jitter); // Max 3 minutes
            
            console.log(`Retry ${retryCount}/${maxRetries} for user ${globalIndex + 1} after ${backoffTime.toFixed(1)}s (Status: ${response.status})`);
            sleep(backoffTime);
        }
        
    } while (retryCount < maxRetries && (response.status >= 500 || response.status === 429 || response.status === 0));

    // Check response
    const isSuccess = check(response, {
        'is status 200': (r) => r.status === 200,
        'response has success status': (r) => {
            try {
                const body = JSON.parse(r.body);
                return body.status && body.status.code === 0;
            } catch (e) {
                return false;
            }
        }
    });

    // Log progress every 500 requests (less frequent to reduce noise)
    if ((globalIndex - START_INDEX) % 500 === 0) {
        const progress = ((globalIndex - START_INDEX + 1) / REMAINING_USERS * 100).toFixed(2);
        console.log(`Progress: User ${globalIndex + 1} (${progress}% of remaining) - LoginId: ${userData.loginId}, Status: ${response.status}`);
    }

    // Log errors for debugging (but less verbose)
    if (!isSuccess) {
        if (response.status >= 500 || response.status === 0) {
            // Server errors - log less frequently to avoid spam
            if ((globalIndex - START_INDEX) % 100 === 0) {
                console.error(`Server error for user ${globalIndex + 1}: Status ${response.status}`);
            }
        } else {
            // Client errors - always log these
            console.error(`Failed to insert user ${globalIndex + 1} (${userData.loginId}): Status ${response.status}`);
            
            // Try to parse error message
            try {
                const errorBody = JSON.parse(response.body);
                if (errorBody.status && errorBody.status.message) {
                    console.log(`Error: ${errorBody.status.message}`);
                }
            } catch (e) {
                // Ignore JSON parse errors
            }
        }
    }
}

// Setup function to validate data availability
export function setup() {
    console.log("=== InsertUserManagement Continue Conservative Load Test Setup ===");
    console.log(`Target URL: ${url}`);
    console.log(`Starting from index: ${START_INDEX}`);
    console.log(`Remaining users to insert: ${REMAINING_USERS}`);
    console.log(`Total users available: ${userManagementData.totalUsers}`);
    console.log(`Authorization: ${authData.username}`);
    console.log(`VUs: 20 (conservative)`);
    console.log(`Sleep duration: ${SLEEP_DURATION}s (conservative)`);
    console.log(`Max retries: 5 (aggressive retry)`);
    console.log(`Total iterations: ${REMAINING_USERS}`);
    console.log(`Executor: shared-iterations (continue conservative mode)`);
    console.log(`Estimated duration: ${(REMAINING_USERS * SLEEP_DURATION / 20 / 60).toFixed(0)} minutes`);
    
    // Validate that we have enough user data
    if (userManagementData.totalUsers < 1000000) {
        throw new Error(`Insufficient user data. Need 1000000 users, but only have ${userManagementData.totalUsers}`);
    }
    
    // Test a sample request to validate configuration
    console.log("Testing sample request...");
    const sampleUser = userManagementData.getUserByIndex(START_INDEX);
    if (!sampleUser) {
        throw new Error(`No sample user data available at index ${START_INDEX}`);
    }
    
    console.log(`Sample user: Index=${START_INDEX}, LoginId=${sampleUser.loginId}, FullName=${sampleUser.fullName}`);
    
    // Show range of users to be processed
    const endIndex = Math.min(START_INDEX + REMAINING_USERS - 1, userManagementData.totalUsers - 1);
    const endUser = userManagementData.getUserByIndex(endIndex);
    if (endUser) {
        console.log(`End user: Index=${endIndex}, LoginId=${endUser.loginId}, FullName=${endUser.fullName}`);
    }
    
    console.log("Setup complete. Starting conservative continuation load test...");
    console.log("Note: Using conservative settings to help server recover from previous load.");
    return {};
}

// Teardown function for final reporting
export function teardown(data) {
    console.log("=== InsertUserManagement Continue Conservative Load Test Complete ===");
    console.log(`Processed users from index ${START_INDEX} onwards`);
    console.log("Check the K6 summary for detailed metrics.");
    console.log("Review logs for any failed insertions that may need manual retry.");
    console.log("If server errors persist, consider further reducing VUs or increasing delays.");
}
