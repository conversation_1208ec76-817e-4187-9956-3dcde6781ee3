import psycopg2
from psycopg2 import sql
from dotenv import load_dotenv
import os
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import base64
import re
import execjs
import json
import hashlib

load_dotenv()

def getNewestUnsignedDocument(NIK):
    
    db_config = {
        'dbname': os.getenv('dataSourceName'),
        'user': os.getenv('dataSourceUsername'),
        'password': os.getenv('dataSourcePassword'),
        'host': os.getenv('dataSourceUrl'),
        'port': os.getenv('dataSourcePort')
    }

    try:
        connection = psycopg2.connect(**db_config)
        cursor = connection.cursor()

        query = '''
        select tdd.document_id from tr_document_h tdh
        join am_msuser am on tdh.id_msuser_customer = am.id_ms_user 
        join tr_document_d tdd on tdd.id_document_h = tdh.id_document_h 
        where am.hashed_id_no = %s and tdd.lov_sign_status = 27 order by tdh.dtm_crt desc limit 1
        '''

        hashedNIK = hashlib.sha256(NIK.encode()).hexdigest()

        cursor.execute(query, (hashedNIK, ))

        result = cursor.fetchone()

        if result is None:
            document_id = ""
        else:
            document_id = result[0]
        
        connection.commit()

    except (Exception, psycopg2.DatabaseError) as error:
        print(f"Error occurred: {error}")
    finally:
        if connection:
            cursor.close()
            connection.close()

    return document_id


def getAesKey():
    
    db_config = {
        'dbname': os.getenv('dataSourceName'),
        'user': os.getenv('dataSourceUsername'),
        'password': os.getenv('dataSourcePassword'),
        'host': os.getenv('dataSourceUrl'),
        'port': os.getenv('dataSourcePort')
    }

    try:
        connection = psycopg2.connect(**db_config)
        cursor = connection.cursor()

        query = '''
        select aes_encrypt_key from ms_tenant
        where tenant_code = 'ADINS'
        '''
        cursor.execute(query)

        result = cursor.fetchone()

        if result is None:
            aesKey = ""
        else : 
            aesKey=  result[0]
            print(aesKey)
        
        connection.commit()

    except (Exception, psycopg2.DatabaseError) as error:
        print(f"Error occurred: {error}")
    finally:
        if connection:
            cursor.close()
            connection.close()

    return aesKey

def encrpyt(data, key):
    cipher = AES.new(key.encode("utf-8"), AES.MODE_ECB)
    plaintext = data.encode("utf-8")
    paddedPlaintext = pad(plaintext, AES.block_size)
    encryptedPlaintext = cipher.encrypt(paddedPlaintext)
    encryptedBase64 = base64.b64encode(encryptedPlaintext).decode("utf-8")
    return encryptedBase64

def updateEncryptDocumentId():
    with open('data/user.js', 'r') as file:
        user = file.read()

    context = execjs.compile(user)
    users = context.eval('user')

    for user in users:
        NIK = user['nik']
        
        documentId = getNewestUnsignedDocument(NIK)

        key = getAesKey()

        encryptDocumentId = encrpyt(documentId, key)

        if encryptDocumentId:
            user['documentId'] = encryptDocumentId

    with open('data/user.js', 'w') as file:
        file.write("const user = \n")
        file.write("    [\n")
        
        for user in users:
            file.write("        {\n")
            file.write(f"            nik: '{user['nik']}',\n")
            file.write(f"            date: '{user['date']}',\n")
            file.write(f"            username: '{user['username']}',\n")
            file.write(f"            nohp: '{user['nohp']}',\n")
            file.write(f"            msg: '{user['msg']}',\n")
            file.write(f"            otp: '{user['otp']}',\n")
            file.write(f"            documentId: '{user['documentId']}',\n")
            file.write(f"            refNumber: '{user['refNumber']}',\n")
            file.write(f"            idSigningProcessAuditTrailDetail: '{user['idSigningProcessAuditTrailDetail']}',\n")
            file.write(f"            bearerToken: '{user['bearerToken']}'\n")
            file.write("        },\n")
        
        file.write("    ];\n")
        file.write("module.exports = user;\n")

# updateEncryptDocumentId()