import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import APIS from "./Data/apis.js";
import baseurl from "./Data/baseurl.js";
import unsignDocument from "./Data/unsignDocumentId.js";
import authorizations from "./Data/authorization-library.js";


let baseurls = baseurl[0];
const url = baseurls.url +"/services/embed/document/resendSignNotif";

const SLEEP_DURATION = 5;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {

    let listDoc = unsignDocument[(exec.vu.idInInstance - 1) % unsignDocument.length];
    let authorization = authorizations[0];

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            //  ,'Cookie' : 'idjs=FDD47A1F028E0E6900095FC1D1395777'
        }
    };

    let apis = APIS[0][exec.vu.idInTest];
 
    if (apis == null) {
        apis = {
        "username": ""
        , "nik": ""
        , "nohp": ""
        };
    }

    const requestBody = JSON.stringify({

        audit: { callerId:"MALVIN" },
        documentId: [listDoc.encryptedDocId],
        msg : authorization.msg,
        tenantCode : "ADINS"
    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    // console.log(url);
    // console.log(tenantKey['xapikey']);
    console.log(response.body);




}
