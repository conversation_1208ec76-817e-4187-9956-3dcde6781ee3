# Troubleshooting Guide - InsertUserManagement Load Test

## Current Status: ⚠️ Configuration Issue Identified

### Issue Summary
Script load testing untuk insert 1 juta user telah berhasil dibuat dan berfungsi secara teknis, namun terdapat **configuration issue** pada sistem yang mencegah eksekusi yang sukses.

## ✅ Yang Berhasil Dikonfirmasi

1. **Endpoint Valid**: `/services/user-management/s/insertUserManagement` tersedia dan merespons
2. **Authentication Working**: Token Bearer berfungsi dengan baik
3. **Request Format Correct**: Format JSON request sesuai dengan spesifikasi
4. **Data Generation Complete**: 1 juta user data unik telah berhasil di-generate
5. **Scripts Ready**: Semua script K6 load testing siap digunakan

## ❌ Issue yang Ditemukan

### Error Message
```
{"status":{"code":10001,"message":"Tenant tidak memiliki role user management."}}
```

### Root Cause Analysis
Berdasarkan business logic yang diberikan:

```java
AmMsrole role2 = daoFactory.getRoleDao().getRoleUserManagementByTenant(request.getRoleCode(),request.getTenantCode());
if (null == role2) {
    throw new UserManagementException(
        getMessage("businesslogic.usermanagement.rolenotisusermanagement", null, audit),
        ReasonUserManagement.ROLE_NOT_EXIST);
}
```

**Masalah**: Tenant "ADINS" tidak memiliki role yang dikonfigurasi sebagai "user management role" di database.

### Roles yang Telah Ditest
- ❌ `ADMCLIENT` → "Role bukan merupakan user management"
- ❌ `ADMERROR` → "Tenant tidak memiliki role user management"
- ❌ `ADMIN` → "Tenant tidak memiliki role user management"
- ❌ `USERMANAGEMENT` → "Tenant tidak memiliki role user management"
- ❌ `SUPERADMIN` → "Tenant tidak memiliki role user management"

### Tenants yang Telah Ditest
- ❌ `ADINS` → Tidak memiliki role user management
- ❌ `WOMF` → Tidak memiliki role user management

## 🔧 Solusi yang Diperlukan

### Option 1: Database Configuration (Recommended)
Tambahkan konfigurasi role user management di database:

```sql
-- Contoh query (sesuaikan dengan struktur database actual)
INSERT INTO am_msrole (role_code, tenant_code, is_user_management, ...)
VALUES ('ADMERROR', 'ADINS', '1', ...);

-- Atau update role existing
UPDATE am_msrole 
SET is_user_management = '1' 
WHERE role_code = 'ADMERROR' AND tenant_code = 'ADINS';
```

### Option 2: Environment Setup
Setup environment testing dengan:
1. Tenant yang memiliki role user management valid
2. User dengan permission yang sesuai
3. Role yang dikonfigurasi dengan benar

### Option 3: Alternative Endpoint
Cari endpoint alternatif untuk insert user yang tidak memerlukan user management role.

## 📋 Files yang Siap Digunakan

### Core Scripts
- ✅ `generateUserData.js` - Generator 1 juta user data
- ✅ `master script/InsertUserManagement.js` - High performance mode
- ✅ `master script/InsertUserManagementBatch.js` - Batch mode (recommended)
- ✅ `master script/InsertUserManagementTest.js` - Testing mode

### Supporting Scripts
- ✅ `getToken.js` - Token generator
- ✅ `testRoleCodes.js` - Role testing
- ✅ `testTenantAndUser.js` - Tenant/user combination testing
- ✅ `testEndpointAvailability.js` - Endpoint validation

### Data Files
- ✅ `master script/Data/userManagementData.js` - 1 juta user data (114MB)
- ✅ `master script/Data/authorization-library.js` - Updated dengan token valid

### Documentation
- ✅ `README_InsertUserManagement.md` - Panduan lengkap
- ✅ `TROUBLESHOOTING_InsertUserManagement.md` - Guide ini

## 🚀 Cara Menjalankan Setelah Issue Resolved

### Quick Test (10 users)
```bash
k6 run "master script/InsertUserManagementTest.js"
```

### Batch Mode (1 million users - recommended)
```bash
k6 run "master script/InsertUserManagementBatch.js"
```

### High Performance Mode (1 million users)
```bash
k6 run "master script/InsertUserManagement.js"
```

## 📊 Expected Performance

### Batch Mode Configuration
- **VUs**: 50 virtual users
- **Iterations**: 20,000 per VU
- **Total**: 1,000,000 requests
- **Duration**: ~3 hours
- **Rate**: ~93 requests/second

### High Performance Mode Configuration
- **VUs**: 100 virtual users
- **Iterations**: 10,000 per VU
- **Total**: 1,000,000 requests
- **Duration**: ~2 hours
- **Rate**: ~139 requests/second

## 🔍 Validation Steps

Setelah configuration issue resolved, jalankan validation:

1. **Test Single User**:
   ```bash
   k6 run "master script/InsertUserManagementTest.js"
   ```

2. **Check Success Response**:
   ```json
   {"status":{"code":0,"message":"Success"}}
   ```

3. **Verify Database**:
   - Check user inserted in `am_msuser` table
   - Verify related records in `ms_useroftenant`, `am_memberofrole`, etc.

4. **Run Full Load Test**:
   ```bash
   k6 run "master script/InsertUserManagementBatch.js"
   ```

## 📞 Next Steps

1. **Contact System Administrator** untuk konfigurasi role user management
2. **Verify Database Schema** untuk memastikan struktur table yang benar
3. **Test dengan Environment Lain** jika tersedia
4. **Consider Alternative Approach** jika endpoint ini tidak dapat dikonfigurasi

## 📝 Notes

- Semua script telah ditest dan berfungsi secara teknis
- Issue hanya pada konfigurasi business logic di server
- Data 1 juta user sudah ready untuk digunakan
- Performance configuration sudah optimal untuk load testing

---

**Status**: Ready to execute once configuration issue is resolved
**Last Updated**: $(Get-Date)
**Contact**: Development Team untuk database configuration
