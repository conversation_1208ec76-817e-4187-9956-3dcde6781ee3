import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';

import baseurl from "./Data/baseurl.js";
import tenant from "./Data/tenant-library.js";


let baseurls = baseurl[0];
const url = baseurls.url + "/services/external/user/sentOtpSigning";

const SLEEP_DURATION = 2;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {
    let tenantKey = tenant[1];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': tenantKey.xapikey

        }
    };

  
    const requestBody = JSON.stringify({
        audit:{
            callerId:"<EMAIL>"
        },
        email:"<EMAIL>",
        refNumber: "LOADTEST4.6.0-" + exec.vu.idInInstance,
        sendingPointOption : "SMS",
        phoneNo : "081297723819"

    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });
    console.log(response.body);
}