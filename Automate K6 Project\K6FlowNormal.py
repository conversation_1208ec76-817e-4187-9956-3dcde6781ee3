

import subprocess
import json
import os
import pandas as pd
import numpy as np
from openpyxl import load_workbook
from datetime import datetime
from createUser import createUser
from getInvitationMsg import updateUserInvitation
from login import updateLogin, updateLoginRegisteredUser
from getOtpUser import updateUserOtpCode, updateUserResetCodeCode
from getNewestDocumentUnsigned import updateUserNewestDocument
from getNewestCanStampDocument import getNewestCanStampDocument
from getOtpInvitationUser import updateInvitationUserOtpCode
from getIdSigningProcessAuditTrailDetail import updateUserIdSigningAuditTrail
from getEncryptDocumentId import updateEncryptDocumentId
from getEmbed import updateUserMsg

output_json_path = 'data/output.json'
excel_output_path = 'data/loadTestReport.xlsx'

rowNum = 1

def run_k6_and_save_output(k6_script_path, output_json_path='output.json'):
    try:
        result = subprocess.run(
            ['k6', 'run', '--out', f'json={output_json_path}', k6_script_path],
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            print(f"Error running k6 script: {result.stderr}")
            return None
        
        output = result.stdout
        print("K6 Output:")
        print(output)

        if not os.path.exists(output_json_path):
            print(f"Output JSON file '{output_json_path}' not found.")
            return None

        json_objects = []
        with open(output_json_path, 'r') as file:
            for line in file:
                try:
                    json_object = json.loads(line)
                    json_objects.append(json_object)
                except json.JSONDecodeError:
                    continue
        
        if json_objects:
            combined_json = json_objects
        else:
            print("No valid JSON objects found in the output file.")
            return None

        api_name = os.path.splitext(os.path.basename(k6_script_path))[0]

        extract_and_calculate_metrics(output_json_path, api_name)

        return combined_json
    
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return None
    
def extract_and_calculate_metrics(file_path, apiName):
    try:
        with open(file_path, 'r') as file:
            json_lines = file.readlines()

        data = [json.loads(line) for line in json_lines]

        # Debugging: Print each entry for inspection
        for entry in data:
            print(entry)  # Check the structure of each entry

        values = [
            entry['data']['value']
            for entry in data
            if entry.get('metric') == 'http_req_duration' and entry.get('type') == 'Point'
        ]

        if not values:
            print("No matching data found.")
            return None

        min_value = int(min(values))
        max_value = int(max(values))
        average_value = int(sum(values) / len(values))
        p95_value = int(np.percentile(values, 95))

        metrics = {
            'min': min_value,
            'max': max_value,
            'average': average_value,
            'p95': p95_value
        }

        create_excel_report(rowNum, metrics, apiName, excel_output_path)

        rowNum + 1

        return metrics
    except FileNotFoundError:
        print(f"File '{file_path}' not found.")
        return None
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON: {e}")
        return None
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return None

def create_excel_report(index, metrics, api_name, output_path):
    current_datetime = datetime.now()
    current_date = current_datetime.strftime('%d/%m/%Y')
    current_time = current_datetime.strftime('%H:%M')
    
    new_data = [
        [index, api_name, "Read", 20, metrics['min'], metrics['max'], metrics['average'], metrics['p95'], 
         current_date, current_time, None, None, None]
    ]

    if os.path.exists(output_path):
        workbook = load_workbook(output_path)
        sheet = workbook.active

        next_row = sheet.max_row + 1

        for row_data in new_data:
            for col_index, value in enumerate(row_data, start=1):
                sheet.cell(row=next_row, column=col_index, value=value)
        
        workbook.save(output_path)
        print(f"Data appended successfully to {output_path}.")
    
    else:
        columns = [
            "No", "API", "Request Type", "Count", "min (ms)", "max (ms)", "mean (ms)", "p95 (ms)",
            "Date", "time", "Resource", "Status", "Note"
        ]
        
        df = pd.DataFrame(new_data, columns=columns)
        df.to_excel(output_path, index=False)
        print(f"Excel report created: {output_path}")


# updateLogin()
# createUser()
# run_k6_and_save_output('k6ScriptNormalFlow/GenerateInvitation.js')
# run_k6_and_save_output('k6ScriptNormalFlow/GenerateInvitationBymenu.js')
# run_k6_and_save_output('k6ScriptNormalFlow/ResendInvitation.js')
# run_k6_and_save_output('k6ScriptNormalFlow/RegenerateInvitation.js')
# updateUserInvitation()
# run_k6_and_save_output('k6ScriptNormalFlow/InvitationRegisterData.js')
# run_k6_and_save_output('k6ScriptNormalFlow/sendOtpEmailInvitation.js')
# updateInvitationUserOtpCode()
# run_k6_and_save_output('k6ScriptNormalFlow/checkOtpEmailInvitation.js') # gaperlu
# run_k6_and_save_output('k6ScriptNormalFlow/RegisterbyInv.js')
# run_k6_and_save_output('k6ScriptNormalFlow/SendOtpActivation.js')
# updateUserOtpCode()
# run_k6_and_save_output('k6ScriptNormalFlow/VerifyOtpActivation.js') # perlu tapi gak perlu
# run_k6_and_save_output('k6ScriptNormalFlow/UpdateActivationStatus.js')
# updateLoginRegisteredUser()
# run_k6_and_save_output('k6ScriptNormalFlow/SendManualDoc.js')
run_k6_and_save_output('k6ScriptNormalFlow/SendDocNormal.js')
updateUserNewestDocument()
run_k6_and_save_output('k6ScriptNormalFlow/ResendNotifSignNormal.js')
updateLoginRegisteredUser()
run_k6_and_save_output('k6ScriptNormalFlow/VerifyLivenessFaceCompareSigning.js')
run_k6_and_save_output('k6ScriptNormalFlow/SendOtpSigningNormal.js')
updateUserOtpCode()
# run_k6_and_save_output('k6ScriptNormalFlow/VerifyOtpSigning.js')  # perlu tapi gak perlu
# run_k6_and_save_output('k6ScriptNormalFlow/ConfirmSignDocument.js')
# run_k6_and_save_output('k6ScriptNormalFlow/SendOtpForgetPassword.js')
# updateUserResetCodeCode()
# run_k6_and_save_output('k6ScriptNormalFlow/CheckResetCode.js')
# run_k6_and_save_output('k6ScriptNormalFlow/GetDetailImportAutosignBm.js')
# getNewestCanStampDocument()
# run_k6_and_save_output('k6ScriptNormalFlow/StampingMeteraiNormal.js')

 
# # EmbedFlow
# run_k6_and_save_output('k6ScriptNormalFlow/SendDocNormal.js')
# updateUserMsg()
# updateEncryptDocumentId()
# run_k6_and_save_output('K6ScriptEmbedFlow/SendOTPSigningEmbed.js')
# run_k6_and_save_output('K6ScriptEmbedFlow/VerifyLivenessFaceCompareSigningEmbed.js')


# # ExternalFlow
# createUser()
# run_k6_and_save_output('K6ScriptExternalFlow/CheckStatusRegist.js')
# run_k6_and_save_output('K6ScriptExternalFlow/GenerateLivenessPrivyExternal.js')
# run_k6_and_save_output('K6ScriptExternalFlow/GenerateInvitationLinkExternal.js')
# run_k6_and_save_output('K6ScriptExternalFlow/RegisterExternal.js')
# run_k6_and_save_output('K6ScriptExternalFlow/SendDocExternal.js')
# updateUserNewestDocument()
# run_k6_and_save_output('K6ScriptExternalFlow/SendOtpSigningExternal.js')
# updateUserOtpCode()
# run_k6_and_save_output('K6ScriptExternalFlow/SignDocExternal.js')
# run_k6_and_save_output('K6ScriptExternalFlow/DownloadDocumentExternal.js')
# run_k6_and_save_output('K6ScriptExternalFlow/SendOtpSigningExternal.js')
# updateUserNewestDocument()
# run_k6_and_save_output('K6ScriptExternalFlow/CancelDocumentExternal.js')
# getNewestCanStampDocument()
# run_k6_and_save_output('K6ScriptExternalFlow/RequestStamping.js')


# # ADMESIGNFlow
# run_k6_and_save_output('K6ScriptADMESIGNFlow/GetGeneralSetting.js')
















