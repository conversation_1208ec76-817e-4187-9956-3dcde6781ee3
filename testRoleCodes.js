import http from "k6/http";
import { check, sleep } from "k6";
import baseurl from "./master script/Data/baseurl.js";
import authorization from "./master script/Data/authorization-library.js";

// Configuration
let baseurls = baseurl[0];
const url = baseurls.url + "/services/user-management/s/insertUserManagement";

// Use <EMAIL> for authorization (index 3)
const authData = authorization[3];

// Test different role codes
const roleCodesToTest = [
    "ADMCLIENT",
    "ADMIN",
    "USERMANAGEMENT",
    "USER_MANAGEMENT",
    "USERMGMT",
    "ADMUSER",
    "USERADMIN",
    "CLIENTADMI<PERSON>",
    "S<PERSON>ERADMIN",
    "SY<PERSON>DMIN"
];

export default function () {
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': authData.bearerToken
        }
    };

    console.log("Testing different role codes for user management...");

    roleCodesToTest.forEach((roleCode, index) => {
        const testLoginId = `ROLETEST.${roleCode}${index}@TEST.COM`;
        
        const requestBody = JSON.stringify({
            "audit": {
                "callerId": "<EMAIL>"
            },
            "fullName": `Test User ${roleCode}`,
            "tenantCode": "ADINS",
            "loginId": testLoginId,
            "roleCode": roleCode,
            "password": "ADINS",
            "officeCode": "0071"
        });

        console.log(`\n--- Testing roleCode: ${roleCode} ---`);
        console.log(`LoginId: ${testLoginId}`);

        const response = http.post(url, requestBody, requestHeader);
        
        console.log(`Status: ${response.status}`);
        console.log(`Response: ${response.body}`);

        try {
            const responseBody = JSON.parse(response.body);
            if (responseBody.status) {
                if (responseBody.status.code === 0) {
                    console.log(`✓ SUCCESS: roleCode "${roleCode}" is valid for user management!`);
                } else {
                    console.log(`✗ FAILED: ${responseBody.status.message}`);
                }
            }
        } catch (e) {
            console.log(`✗ FAILED: Could not parse response`);
        }

        sleep(1); // Wait 1 second between tests
    });
}

export let options = {
    vus: 1,
    iterations: 1,
};
