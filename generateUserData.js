/**
 * <PERSON><PERSON><PERSON> untuk generate 1 juta data user unik untuk load testing InsertUserManagement
 * <PERSON><PERSON><PERSON><PERSON><PERSON> kombinasi loginId dan fullName yang unik
 * Node.js version
 */

const fs = require('fs');
const path = require('path');

function generateUniqueLoginId(index) {
    // Menggunakan berbagai prefix untuk variasi
    const prefixes = ['ANDY', 'BETA', 'CHARLIE', 'DELTA', 'ECHO', 'FOXTROT', 'GOLF', 'HOTEL', 'INDIA', 'JULIET'];
    const prefix = prefixes[index % prefixes.length];
    return `USERMANAGEMENT.${prefix}${index}@TEST.COM`;
}

function generateUniqueFullName(index) {
    const firstNames = [
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
    ];
    
    const last<PERSON><PERSON> = [
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', 'Wilson', 'Anderson', 'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin',
        'Lee', 'Perez', 'Thompson', 'White', 'Harris', 'Sanchez', 'Clark', 'Ramirez', 'Lewis', 'Robinson',
        'Walker', 'Young', 'Allen', 'King', 'Wright', 'Scott', 'Torres', 'Nguyen', 'Hill', 'Flores',
        'Green', 'Adams', 'Nelson', 'Baker', 'Hall', 'Rivera', 'Campbell', 'Mitchell', 'Carter', 'Roberts'
    ];
    
    // Kombinasi nama dengan index untuk memastikan keunikan
    const firstName = firstNames[index % firstNames.length];
    const lastName = lastNames[Math.floor(index / firstNames.length) % lastNames.length];
    
    // Tambahkan suffix numerik jika diperlukan untuk keunikan
    if (index >= (firstNames.length * lastNames.length)) {
        const suffix = Math.floor(index / (firstNames.length * lastNames.length));
        return `${firstName} ${lastName} ${suffix}`;
    } else {
        return `${firstName} ${lastName}`;
    }
}

function generateUserData(totalUsers = 1000000) {
    console.log(`Generating ${totalUsers.toLocaleString()} user data...`);
    
    const users = [];
    
    for (let i = 0; i < totalUsers; i++) {
        if (i % 10000 === 0) {
            console.log(`Progress: ${i.toLocaleString()}/${totalUsers.toLocaleString()} (${(i/totalUsers*100).toFixed(1)}%)`);
        }
        
        const user = {
            loginId: generateUniqueLoginId(i),
            fullName: generateUniqueFullName(i),
            index: i
        };
        users.push(user);
    }
    
    return users;
}

function saveToJsFile(users, filename = "master script/Data/userManagementData.js") {
    console.log(`Saving ${users.length.toLocaleString()} users to ${filename}...`);
    
    // Ensure directory exists
    const dir = path.dirname(filename);
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
    
    // Split data menjadi chunks untuk menghindari file terlalu besar
    const chunkSize = 50000; // 50k users per chunk
    const chunks = [];
    for (let i = 0; i < users.length; i += chunkSize) {
        chunks.push(users.slice(i, i + chunkSize));
    }
    
    let fileContent = '';
    fileContent += '// Auto-generated user data for InsertUserManagement load testing\n';
    fileContent += `// Generated on: ${new Date().toISOString()}\n`;
    fileContent += `// Total users: ${users.length.toLocaleString()}\n\n`;
    
    fileContent += 'const userManagementData = {\n';
    fileContent += `    totalUsers: ${users.length},\n`;
    fileContent += `    chunkSize: ${chunkSize},\n`;
    fileContent += '    chunks: [\n';
    
    chunks.forEach((chunk, chunkIndex) => {
        const startIndex = chunkIndex * chunkSize;
        const endIndex = Math.min((chunkIndex + 1) * chunkSize - 1, users.length - 1);
        
        fileContent += `        // Chunk ${chunkIndex + 1}: users ${startIndex} - ${endIndex}\n`;
        fileContent += '        [\n';
        
        chunk.forEach(user => {
            fileContent += `            {"loginId": "${user.loginId}", "fullName": "${user.fullName}", "index": ${user.index}},\n`;
        });
        
        fileContent += '        ],\n';
    });
    
    fileContent += '    ],\n';
    fileContent += '    \n';
    fileContent += '    // Helper function to get user by global index\n';
    fileContent += '    getUserByIndex: function(index) {\n';
    fileContent += '        const chunkIndex = Math.floor(index / this.chunkSize);\n';
    fileContent += '        const userIndex = index % this.chunkSize;\n';
    fileContent += '        if (chunkIndex < this.chunks.length && userIndex < this.chunks[chunkIndex].length) {\n';
    fileContent += '            return this.chunks[chunkIndex][userIndex];\n';
    fileContent += '        }\n';
    fileContent += '        return null;\n';
    fileContent += '    }\n';
    fileContent += '};\n\n';
    fileContent += 'module.exports = userManagementData;\n';
    
    fs.writeFileSync(filename, fileContent, 'utf8');
    
    console.log(`Successfully saved ${users.length.toLocaleString()} users to ${filename}`);
    console.log(`File size: ${chunks.length} chunks of ${chunkSize.toLocaleString()} users each`);
}

function main() {
    console.log('=== User Management Data Generator ===');
    console.log('Generating 1 million unique users for InsertUserManagement load testing');
    
    const startTime = Date.now();
    
    // Generate user data
    const users = generateUserData(1000000);
    
    // Save to JavaScript file
    saveToJsFile(users);
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log('\n=== Generation Complete ===');
    console.log(`Total users generated: ${users.length.toLocaleString()}`);
    console.log(`Generation time: ${duration.toFixed(1)} seconds`);
    console.log('File: master script/Data/userManagementData.js');
    console.log('\nSample data:');
    for (let i = 0; i < Math.min(5, users.length); i++) {
        const user = users[i];
        console.log(`  ${i+1}. LoginId: ${user.loginId}, FullName: ${user.fullName}`);
    }
}

if (require.main === module) {
    main();
}
