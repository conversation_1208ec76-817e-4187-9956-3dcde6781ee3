import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import APIS from "./Data/apis.js";
import tenant from "./Data/tenant-library.js";
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const url = "http://localhost:9001/services/saldo/s/listBalanceMutation";
// const url = "https://esignhub.ad-ins.com/adimobile/esign/services/saldo/s/listBalanceMutation";
const SLEEP_DURATION = 2;
const USERNAME = "<EMAIL>";
const PASSWORD = "supersupeR4!";
const USERNAME_2 = "<EMAIL>";


// setup per iteration
// export let options = {
//     discardResponseBodies: false,
//     scenarios: {

//         contacts: {
//             executor: 'per-vu-iterations',
//             vus: 30,
//             iterations: 2,
//             maxDuration: '10m',
//         },
//     },
// };

export let options = {
     vus: 1, // 10 virtual users
     duration: '1s', // duration of the test
};

//export let options = {
//    discardResponseBodies: false,
//    stages: [
//        { duration: '30', target: 100 },
//      { duration: '30s', target: 200 }, // ramp up to 20 VUs over 1 minute
//      { duration: '20m', target: 200 } // ramp up to 20 VUs over 1 minute        
//    ],
//  };


// Test scenario
export default function () {
    let tenantKey = tenant[0][1];
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'Authorization': 'Bearer BatRdHsMLqMfv3Muk6gfREYfJ5Y='
            //  ,'Cookie' : 'idjs=FDD47A1F028E0E6900095FC1D1395777'
        }
    };

    // Random e-mail suffix

    let apis = APIS[0][exec.vu.idInTest - 1];

    if (apis == null) {
        apis = {
            "username": ""
            , "nik": ""
            , "nohp": ""
        };
    }
    // Get User Profile (user/s/profiles)
    const requestBody = JSON.stringify({

        audit: { callerId: "UserIBAA" },
        balanceType: "SGN",
        documentName: "",
        documentType: "",
        officeCode: "",
        page: 1,
        referenceNo: "",
        tenantCode: "ADINS",
        transactionDateEnd: "2024-03-14",
        transactionDateStart: "2024-02-01",
        transactionType: "",
        vendorCode: "VIDA"

    });

    const response = http.post(url, requestBody, requestHeader);
  
    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200

    });

  // console.log(response.body);

}
