import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import signedDocument from "./Data/DocumentId.js";
import authorizations from "./Data/authorization-library.js";
import baseurl from "./Data/baseurl.js";

let baseurls = baseurl[0];
const url = baseurls.url + "/services/document/s/startStampingMeterai";
const SLEEP_DURATION = 1;

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '1m',
        },
    },
};

// Test scenario
export default function () {
    
    let listDoc = signedDocument[(exec.vu.idInInstance - 1) % signedDocument.length];
    let authorization = authorizations[3];

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'Authorization': authorization.bearerToken
        }
    };

    const requestBody = JSON.stringify({

        audit: {
            callerId: "<EMAIL>"
        },
        refNumber: listDoc.refNumber,
        tenantCode: "ADINS"

    });
    
    const response = http.post(url, requestBody, requestHeader);

    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200
    });

    console.log([exec.vu.idInTest - 1], response.body);

}
