import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import refNumber from "./Data/refNumber.js";
import baseurl from "./Data/baseurl.js";
import signedDocument from "./Data/DocumentId.js";
import authorizations from "./Data/authorization-library.js";

let baseurls = baseurl[0];
const url = baseurls.url + "/services/embed/document/startStampingMateraiEmbed";

const SLEEP_DURATION = 5;



// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {
    let listDoc = signedDocument[(exec.vu.idInInstance - 1) % signedDocument.length];
    let authorization = authorizations[3];

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
        }
    };

  




    const requestBody = JSON.stringify({
        msg: authorization.msg,
        tenantCode: "ADINS",
        refNumber: listDoc.refNumber,
        audit:{
            callerId:"CONFINS"
        }
    }, null, "\t");

    sleep(SLEEP_DURATION);
    const response = http.post(url, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200,

    });

    console.log(response.body);




}
