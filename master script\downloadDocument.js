import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';

const url = "http://8.215.11.137/adimobile/esign/services/external/document/downloadDocument";
const SLEEP_DURATION = 1;
const DOCUMENT_IDS = [
    "00163E03-FD4A-88F2-11EE-E2983B6E3A10",
"00163E03-FD4A-88F2-11EE-E2983B782520",
"00163E03-FD4A-88F2-11EE-E2983B787340",
"00163E03-FD4A-88F2-11EE-E2983B8DA8F0",
"00163E03-FD4A-88F2-11EE-E2983B8C4960",
"00163E03-FD4A-88F2-11EE-E2983B740670",
"00163E03-FD4A-88F2-11EE-E2983B9EE700",
"00163E03-FD4A-88F2-11EE-E2983B9A0500",
"00163E03-FD4A-88F2-11EE-E2983BB10F70",
"00163E03-FD4A-88F2-11EE-E2983BAAA6D0",
"00163E03-FD4A-88F2-11EE-E2983BB8FEB0",
"00163E03-FD4A-88F2-11EE-E2983BBAAC60",
"00163E03-FD4A-88F2-11EE-E2983BC29BA0",
"00163E03-FD4A-88F2-11EE-E2983BC533B0",
"00163E03-FD4A-88F2-11EE-E2983BC819E0",
"00163E03-FD4A-88F2-11EE-E2983BCF1EC0",
"00163E03-FD4A-88F2-11EE-E2983BCCFBE0",
"00163E03-FD4A-88F2-11EE-E2983BD400C0",
"00163E03-FD4A-88F2-11EE-E2983BD9A610",
"00163E03-FD4A-88F2-11EE-E2983BD0F380",
"00163E03-FD4A-88F2-11EE-E2983BDEFD41",
"00163E03-FD4A-88F2-11EE-E2983BDEFD40",
"00163E03-FD4A-88F2-11EE-E2983BDC8C40",
"00163E03-FD4A-88F2-11EE-E2983BD9A611",
"00163E03-FD4A-88F2-11EE-E2983BDB05A0",
"00163E03-FD4A-88F2-11EE-E2983BD53940",
"00163E03-FD4A-88F2-11EE-E2983BCD7110",
"00163E03-FD4A-88F2-11EE-E2983C3867E0",
"00163E03-FD4A-88F2-11EE-E2983CFFAF80",
"00163E03-FD4A-88F2-11EE-E2983BF89FC0"

];

// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {
        contacts: {
            executor: 'per-vu-iterations',
            vus: 8000,
            iterations: 600, // 10 minutes with 1 iteration per second
            maxDuration: '10m',
        },
    },
};

// Test scenario
export default function () {
    const vuID = exec.scenarioVUId; // Get the VU ID for the current iteration
    const documentID = DOCUMENT_IDS[vuID % DOCUMENT_IDS.length]; // Get the document ID for the current VU

    const requestHeader = {
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'x-api-key': 'Gy6Ka5XhgRmq@ADINS',
        }
    };

    const requestBody = JSON.stringify({
        audit: {
            callerId: "USER"
        },
        documentId: documentID // Use the document ID for this iteration
    });

    const response = http.post(url, requestBody, requestHeader);

    sleep(SLEEP_DURATION);

    check(response, {
        'is status 200': (r) => r.status === 200
    });

    console.log("VU ID:", vuID, "Document ID:", documentID, "Status:", response.status);
}
