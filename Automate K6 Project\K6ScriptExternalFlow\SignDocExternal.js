import http from "k6/http";
import { check, sleep } from "k6";
import exec from 'k6/execution';
import baseurl from "../data/baseurl.js";
import users from "../data/user.js";
import tenants from "../data/apiKey.js";

let baseurls = baseurl[0];
const URL = baseurls.url  +  "/services/external/document/signDocument";
let tenant = tenants[1];
const SLEEP_DURATION = 1;


// setup per iteration
export let options = {
    discardResponseBodies: false,
    scenarios: {

        contacts: {
            executor: 'per-vu-iterations',
            vus: 20,
            iterations: 1,
            maxDuration: '15m',
        },
    },
};

// Test scenario
export default function () {
    const requestHeader = {
        headers: {
            'Content-Type': 'application/json'
            , 'Accept': 'application/json'
            , 'x-api-key': tenant.xapikey
        }
    };
    
    let user = users[exec.vu.idInTest - 1];
 
    if (user == null) {
        user = {
        "username": ""
        , "nik": ""
        , "nohp": ""
        };
    }

    const requestBody = JSON.stringify({

        
        audit: {
            callerId: "default"
        },
        documentId: [user.documentId]
        ,email: user.username + "esignhub.my.id"
        ,password : "Password123!"
        ,ipAddress : "***********"
        , browserInfo : "Browser Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
        ,otp : user.otp
        ,selfPhoto : ""

    });
    
    const response = http.post(URL, requestBody, requestHeader);
    check(response, {
        'is status 200': (r) => r.status === 200
    });

    console.log(response.body);

}
