import http from "k6/http";
import { check, sleep } from "k6";
import baseurl from "./master script/Data/baseurl.js";
import authorization from "./master script/Data/authorization-library.js";

// Configuration
let baseurls = baseurl[0];
const url = baseurls.url + "/services/user-management/s/insertUserManagement";

// Test different combinations
const testCombinations = [
    {
        authIndex: 3, // <EMAIL>
        tenantCode: "ADINS",
        roleCode: "ADMCLIENT",
        description: "<EMAIL> with ADINS tenant and ADMCLIENT role"
    },
    {
        authIndex: 3, // <EMAIL>
        tenantCode: "WOMF",
        roleCode: "ADMCLIENT",
        description: "<EMAIL> with WOMF tenant and ADMCLIENT role"
    },
    {
        authIndex: 2, // <EMAIL>
        tenantCode: "WOMF",
        roleCode: "ADMCLIENT",
        description: "<PERSON>MI<PERSON>@WOM.CO.ID with WOMF tenant and ADMCLIENT role"
    },
    {
        authIndex: 2, // <EMAIL>
        tenantCode: "ADINS",
        roleCode: "ADMCLIENT",
        description: "<EMAIL> with ADINS tenant and ADMCLIENT role"
    },
    {
        authIndex: 1, // HELPDESK@ADINS
        tenantCode: "ADINS",
        roleCode: "ADMCLIENT",
        description: "HELPDESK@ADINS with ADINS tenant and ADMCLIENT role"
    },
    {
        authIndex: 0, // <EMAIL>
        tenantCode: "ADINS",
        roleCode: "ADMCLIENT",
        description: "<EMAIL> with ADINS tenant and ADMCLIENT role"
    }
];

export default function () {
    console.log("Testing different tenant and user combinations for user management...");

    testCombinations.forEach((combo, index) => {
        const authData = authorization[combo.authIndex];
        const testLoginId = `TENANTTEST.${combo.tenantCode}${index}@TEST.COM`;
        
        const requestHeader = {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': authData.bearerToken
            }
        };

        const requestBody = JSON.stringify({
            "audit": {
                "callerId": authData.username
            },
            "fullName": `Test User ${combo.tenantCode} ${index}`,
            "tenantCode": combo.tenantCode,
            "loginId": testLoginId,
            "roleCode": combo.roleCode,
            "password": "ADINS",
            "officeCode": "0071"
        });

        console.log(`\n--- Test ${index + 1}: ${combo.description} ---`);
        console.log(`Auth User: ${authData.username}`);
        console.log(`Tenant: ${combo.tenantCode}`);
        console.log(`Role: ${combo.roleCode}`);
        console.log(`LoginId: ${testLoginId}`);

        const response = http.post(url, requestBody, requestHeader);
        
        console.log(`Status: ${response.status}`);
        console.log(`Response: ${response.body}`);

        try {
            const responseBody = JSON.parse(response.body);
            if (responseBody.status) {
                if (responseBody.status.code === 0) {
                    console.log(`✓ SUCCESS: This combination works for user management!`);
                    console.log(`✓ Use: Auth=${authData.username}, Tenant=${combo.tenantCode}, Role=${combo.roleCode}`);
                } else {
                    console.log(`✗ FAILED: ${responseBody.status.message}`);
                }
            }
        } catch (e) {
            if (response.status === 401) {
                console.log(`✗ FAILED: Authentication failed - token may be expired`);
            } else {
                console.log(`✗ FAILED: Could not parse response`);
            }
        }

        sleep(1); // Wait 1 second between tests
    });
}

export let options = {
    vus: 1,
    iterations: 1,
};
